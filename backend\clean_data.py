from app.models.database import engine
from app.models.risque import Risque
from sqlalchemy.orm import sessionmaker

Session = sessionmaker(bind=engine)
db = Session()

try:
    # Supprimer la ligne d'en-tête
    risque_entete = db.query(Risque).filter(Risque.processus == 'Processus').first()
    if risque_entete:
        print(f"Suppression de la ligne d'en-tête: ID {risque_entete.id} - {risque_entete.description}")
        db.delete(risque_entete)
        db.commit()
        print("Ligne d'en-tête supprimée avec succès")
    else:
        print("Aucune ligne d'en-tête trouvée")
    
    # Vérifier le nouveau total
    total = db.query(Risque).count()
    print(f"Nouveau total: {total} risques")
    
    # Vérifier la distribution
    print("\nNouvelle distribution par processus:")
    processus_count = {}
    risques = db.query(Risque).all()
    for r in risques:
        if r.processus:
            processus_count[r.processus] = processus_count.get(r.processus, 0) + 1
    
    for processus, count in sorted(processus_count.items()):
        print(f"  {processus}: {count} risques")

finally:
    db.close()
