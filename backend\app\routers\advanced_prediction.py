from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List
import asyncio
from datetime import datetime

from ..models.database import get_db
from ..models.risque import Risque
from ..schemas import PredictionRequest, PredictionResponse
from ..ml.advanced_risk_predictor import AdvancedRiskPredictor

router = APIRouter(prefix="/api/predict/advanced", tags=["advanced_prediction"])

# Instance globale du prédicteur avancé
advanced_predictor = AdvancedRiskPredictor()

@router.post("/train")
async def train_advanced_models(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Entraîne les modèles ML avancés en arrière-plan"""
    
    try:
        # Récupérer tous les risques
        risques = db.query(Risque).all()
        
        if len(risques) < 10:
            raise HTTPException(
                status_code=400,
                detail="Pas assez de données pour l'entraînement (minimum 10 risques)"
            )
        
        # Convertir en DataFrame
        import pandas as pd
        
        data = []
        for risque in risques:
            data.append({
                'description': risque.description,
                'causes': risque.causes,
                'effets': risque.effets,
                'processus': risque.processus,
                'note_detection': risque.note_detection,
                'note_gravite': risque.note_gravite,
                'note_frequence': risque.note_frequence,
                'ipr': risque.ipr,
                'niveau_criticite': risque.niveau_criticite
            })
        
        df = pd.DataFrame(data)
        
        # Lancer l'entraînement en arrière-plan
        def train_models():
            try:
                global advanced_predictor
                metrics = advanced_predictor.train(df)
                print(f"✅ Entraînement terminé: {metrics}")
            except Exception as e:
                print(f"❌ Erreur entraînement: {e}")
        
        background_tasks.add_task(train_models)
        
        return {
            "status": "training_started",
            "message": "Entraînement des modèles avancés démarré en arrière-plan",
            "data_size": len(risques),
            "estimated_time": "2-5 minutes"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors du démarrage de l'entraînement: {str(e)}"
        )

@router.post("/predict", response_model=PredictionResponse)
async def predict_advanced(
    prediction_request: PredictionRequest,
    db: Session = Depends(get_db)
):
    """Prédiction avancée avec ensemble de modèles ML"""
    
    try:
        # Vérifier si les modèles sont entraînés
        if not advanced_predictor.is_trained:
            # Essayer de charger les modèles existants
            if not advanced_predictor.load_models():
                raise HTTPException(
                    status_code=503,
                    detail="Modèles non entraînés. Utilisez /train d'abord."
                )
        
        # Faire la prédiction
        result = advanced_predictor.predict(
            description=prediction_request.description,
            causes=prediction_request.causes,
            effets=prediction_request.effets,
            processus=prediction_request.processus,
            use_ensemble=True
        )
        
        return PredictionResponse(
            niveau_predit=result['niveau_predit'],
            confiance=result['confiance'],
            facteurs_influents=result['facteurs_influents'],
            probabilites=result.get('probabilites', {}),
            model_used=result.get('model_used', 'Advanced Ensemble'),
            feature_importance=result.get('feature_importance', [])
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la prédiction avancée: {str(e)}"
        )

@router.get("/models/info")
async def get_models_info():
    """Informations sur les modèles avancés"""
    
    try:
        info = advanced_predictor.get_model_info()
        return info
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la récupération des infos: {str(e)}"
        )

@router.get("/models/performance")
async def get_models_performance():
    """Performance détaillée des modèles"""
    
    try:
        if not advanced_predictor.is_trained:
            return {
                "status": "not_trained",
                "message": "Modèles non entraînés"
            }
        
        return {
            "status": "trained",
            "performance": advanced_predictor.performance_metrics,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la récupération des performances: {str(e)}"
        )

@router.post("/retrain")
async def retrain_models(
    background_tasks: BackgroundTasks,
    force: bool = False,
    db: Session = Depends(get_db)
):
    """Réentraîne les modèles si nécessaire"""
    
    try:
        # Récupérer les données
        risques = db.query(Risque).all()
        
        if len(risques) < 10:
            raise HTTPException(
                status_code=400,
                detail="Pas assez de données pour le réentraînement"
            )
        
        import pandas as pd
        
        data = []
        for risque in risques:
            data.append({
                'description': risque.description,
                'causes': risque.causes,
                'effets': risque.effets,
                'processus': risque.processus,
                'note_detection': risque.note_detection,
                'note_gravite': risque.note_gravite,
                'note_frequence': risque.note_frequence,
                'ipr': risque.ipr,
                'niveau_criticite': risque.niveau_criticite
            })
        
        df = pd.DataFrame(data)
        
        # Vérifier si le réentraînement est nécessaire
        def check_and_retrain():
            try:
                global advanced_predictor
                if force:
                    print("🔄 Réentraînement forcé...")
                    metrics = advanced_predictor.train(df)
                    print(f"✅ Réentraînement forcé terminé: {metrics}")
                else:
                    retrained = advanced_predictor.retrain_if_needed(df)
                    if retrained:
                        print("✅ Réentraînement automatique terminé")
                    else:
                        print("✅ Réentraînement non nécessaire")
            except Exception as e:
                print(f"❌ Erreur réentraînement: {e}")
        
        background_tasks.add_task(check_and_retrain)
        
        return {
            "status": "retrain_started",
            "message": "Vérification et réentraînement démarrés",
            "force": force,
            "data_size": len(risques)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors du réentraînement: {str(e)}"
        )

@router.get("/features/analysis")
async def get_feature_analysis():
    """Analyse des features utilisées par les modèles"""
    
    try:
        if not advanced_predictor.is_trained:
            return {
                "status": "not_trained",
                "message": "Modèles non entraînés"
            }
        
        return {
            "status": "available",
            "keyword_categories": advanced_predictor.critical_keywords,
            "low_risk_keywords": advanced_predictor.low_risk_keywords,
            "feature_types": {
                "textual": "TF-IDF vectorization (1000 features, 1-3 grams)",
                "numeric": "Scaled numeric features",
                "categorical": "Encoded categorical features",
                "engineered": "Custom engineered features"
            },
            "total_features": "~1050+ features"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de l'analyse des features: {str(e)}"
        )

@router.get("/examples/advanced")
async def get_advanced_examples():
    """Exemples optimisés pour les modèles avancés"""
    
    return [
        {
            "titre": "Risque Critique - Panne Système",
            "processus": "Infrastructures et IT",
            "description": "Panne critique du serveur principal causant un arrêt total de la production. Impact majeur sur les opérations avec perte financière importante.",
            "causes": "Défaillance matérielle du serveur, absence de redondance, maintenance préventive insuffisante",
            "effets": "Arrêt complet de la production, perte de données, impact client majeur, coûts de récupération élevés",
            "niveau_attendu": "Critique (IPR > 18)"
        },
        {
            "titre": "Risque Mineur - Erreur Facturation",
            "processus": "Facturation et recouvrement", 
            "description": "Erreurs occasionnelles dans le calcul des factures clients dues à un problème de paramétrage du système.",
            "causes": "Configuration incorrecte du système de facturation, formation insuffisante des opérateurs",
            "effets": "Réclamations clients, temps de correction, impact mineur sur la satisfaction",
            "niveau_attendu": "Mineur (8 ≤ IPR ≤ 18)"
        },
        {
            "titre": "Risque Tolérable - Formation Personnel",
            "processus": "Gestion des ressources humaines",
            "description": "Formation du personnel pas toujours à jour sur les nouvelles procédures. Amélioration continue nécessaire.",
            "causes": "Planning de formation chargé, évolution rapide des procédures, budget formation limité",
            "effets": "Légère baisse d'efficacité temporaire, adaptation plus lente aux changements",
            "niveau_attendu": "Tolérable (IPR < 8)"
        }
    ]
