"""
Script pour créer des données d'exemple dans la base de données
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.models.database import engine, Base
from app.models.risque import Risque, Opportunite
from datetime import datetime

# Créer les tables
Base.metadata.create_all(bind=engine)

# Créer une session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_sample_risks():
    """Crée quelques risques d'exemple"""
    
    db = SessionLocal()
    
    try:
        # Vérifier s'il y a déjà des données
        existing_count = db.query(Risque).count()
        if existing_count > 0:
            print(f"Il y a déjà {existing_count} risques dans la base de données.")
            return
        
        # Créer des risques d'exemple
        sample_risks = [
            {
                "processus": "Gestion des achats",
                "source": "Interne",
                "enjeux": "Financier",
                "description": "Retard de livraison des fournisseurs",
                "causes": "Problèmes logistiques, grèves",
                "effets": "Arrêt de production, perte de chiffre d'affaires",
                "moyens_detection": "Suivi des commandes",
                "note_detection": 3,
                "note_gravite": 4,
                "note_frequence": 2,
                "moyens_maitrise": "Diversification des fournisseurs",
                "actions_risques": "Mettre en place un plan de continuité",
                "responsables": "Responsable achats",
                "delai_realisation": "3 mois",
                "methode_evaluation": "Audit interne",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "processus": "Gestion RH",
                "source": "Externe",
                "enjeux": "Social",
                "description": "Pénurie de compétences techniques",
                "causes": "Marché de l'emploi tendu",
                "effets": "Retard dans les projets",
                "moyens_detection": "Indicateurs RH",
                "note_detection": 2,
                "note_gravite": 3,
                "note_frequence": 3,
                "moyens_maitrise": "Formation interne",
                "actions_risques": "Plan de formation et recrutement",
                "responsables": "DRH",
                "delai_realisation": "6 mois",
                "methode_evaluation": "Entretiens annuels",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "processus": "Gestion financière",
                "source": "Externe",
                "enjeux": "Financier",
                "description": "Fluctuation des taux de change",
                "causes": "Instabilité économique mondiale",
                "effets": "Impact sur la rentabilité",
                "moyens_detection": "Suivi quotidien des taux",
                "note_detection": 1,
                "note_gravite": 3,
                "note_frequence": 4,
                "moyens_maitrise": "Couverture de change",
                "actions_risques": "Mise en place d'instruments financiers",
                "responsables": "Directeur financier",
                "delai_realisation": "1 mois",
                "methode_evaluation": "Reporting mensuel",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        ]
        
        # Insérer les risques
        for risk_data in sample_risks:
            risk = Risque(**risk_data)
            risk.update_calculated_fields()  # Calcule IPR et niveau de criticité
            db.add(risk)
        
        db.commit()
        print(f"✅ {len(sample_risks)} risques d'exemple créés avec succès!")
        
        # Afficher les statistiques
        total_risks = db.query(Risque).count()
        print(f"Total des risques dans la base: {total_risks}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Création de données d'exemple")
    print("=" * 50)
    create_sample_risks()
