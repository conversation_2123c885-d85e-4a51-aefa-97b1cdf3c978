from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from datetime import datetime
import os

from ..models.database import get_db
from ..schemas import ExportRequest, ExportResponse
from ..services.export_service import ExportService

router = APIRouter(prefix="/api/export", tags=["export"])

@router.post("/pdf", response_model=ExportResponse)
def export_risques_pdf(
    export_request: ExportRequest,
    db: Session = Depends(get_db)
):
    """Exporte les risques en format PDF"""
    try:
        print(f"🔍 Template reçu côté backend: {export_request.template}")
        print(f"📄 Format: {export_request.format}")
        filename = ExportService.export_risques_pdf(db, export_request)
        download_url = ExportService.get_export_url(filename)
        
        return ExportResponse(
            filename=filename,
            download_url=download_url,
            created_at=datetime.now()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'export PDF: {str(e)}")

@router.post("/word", response_model=ExportResponse)
def export_risques_word(
    export_request: ExportRequest,
    db: Session = Depends(get_db)
):
    """Exporte les risques en format Word"""
    try:
        filename = ExportService.export_risques_word(db, export_request)
        download_url = ExportService.get_export_url(filename)
        
        return ExportResponse(
            filename=filename,
            download_url=download_url,
            created_at=datetime.now()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'export Word: {str(e)}")

@router.get("/download/{filename}")
def download_file(filename: str):
    """Télécharge un fichier exporté"""
    filepath = os.path.join("static", filename)
    
    if not os.path.exists(filepath):
        raise HTTPException(status_code=404, detail="Fichier non trouvé")
    
    # Déterminer le type de contenu
    if filename.endswith('.pdf'):
        media_type = 'application/pdf'
    elif filename.endswith('.docx'):
        media_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    else:
        media_type = 'application/octet-stream'
    
    return FileResponse(
        path=filepath,
        media_type=media_type,
        filename=filename
    )

@router.get("/templates")
def get_export_templates():
    """Récupère la liste des templates d'export disponibles"""
    return {
        "templates": [
            {
                "id": "standard",
                "name": "Rapport Standard",
                "description": "Rapport complet avec résumé exécutif et détail des risques",
                "formats": ["pdf", "word"]
            },
            {
                "id": "summary",
                "name": "Résumé Exécutif",
                "description": "Rapport de synthèse avec statistiques principales",
                "formats": ["pdf", "word"]
            }
        ]
    }

@router.get("/filters")
def get_export_filters(db: Session = Depends(get_db)):
    """Récupère les options de filtrage disponibles pour l'export"""
    
    # Récupérer les processus uniques
    from sqlalchemy import distinct
    from ..models.risque import Risque
    
    processus = db.query(distinct(Risque.processus)).all()
    processus_list = [p[0] for p in processus if p[0]]
    
    # Niveaux de criticité disponibles
    criticite_levels = [
        "Tolérable (IPR < 8)",
        "Mineur (8 ≤ IPR ≤ 18)",
        "Critique (IPR > 18)"
    ]
    
    return {
        "processus": processus_list,
        "niveaux_criticite": criticite_levels,
        "formats": ["pdf", "word"]
    }
