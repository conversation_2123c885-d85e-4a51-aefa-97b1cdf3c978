from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import os
import uuid

from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

from ..models.risque import Risque, Opportunite
from ..schemas import ExportRequest

class ExportService:
    
    @staticmethod
    def export_risques_pdf(
        db: Session, 
        export_request: ExportRequest,
        filename: Optional[str] = None
    ) -> str:
        """Exporte les risques en format PDF"""
        
        if not filename:
            filename = f"rapport_risques_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        
        filepath = os.path.join("static", filename)
        
        # Créer le répertoire static s'il n'existe pas
        os.makedirs("static", exist_ok=True)
        
        # Récupérer les données filtrées
        risques = ExportService._get_filtered_risques(db, export_request)
        
        # Créer le document PDF
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        
        # Styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.darkblue
        )
        
        # Titre du rapport selon le template
        if export_request.template == "summary":
            titre = "Résumé Exécutif - Analyse des Risques"
        else:
            titre = "Rapport Complet d'Analyse des Risques"

        story.append(Paragraph(titre, title_style))
        story.append(Paragraph(f"Type: {export_request.template.title()}", styles['Normal']))
        story.append(Paragraph(f"Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # Résumé exécutif
        story.append(Paragraph("Résumé Exécutif", heading_style))
        
        # Statistiques générales
        total_risques = len(risques)
        risques_critiques = len([r for r in risques if "Critique" in r.niveau_criticite])
        risques_mineurs = len([r for r in risques if "Mineur" in r.niveau_criticite])
        risques_tolerables = len([r for r in risques if "Tolérable" in r.niveau_criticite])
        ipr_moyen = sum([r.ipr for r in risques]) / total_risques if total_risques > 0 else 0
        
        summary_data = [
            ["Métrique", "Valeur"],
            ["Nombre total de risques", str(total_risques)],
            ["Risques critiques", str(risques_critiques)],
            ["Risques mineurs", str(risques_mineurs)],
            ["Risques tolérables", str(risques_tolerables)],
            ["IPR moyen", f"{ipr_moyen:.2f}"]
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # Distribution par processus
        processus_stats = {}
        for risque in risques:
            if risque.processus not in processus_stats:
                processus_stats[risque.processus] = 0
            processus_stats[risque.processus] += 1
        
        story.append(Paragraph("Distribution par Processus", heading_style))
        
        processus_data = [["Processus", "Nombre de Risques"]]
        for processus, count in sorted(processus_stats.items()):
            processus_data.append([processus, str(count)])
        
        processus_table = Table(processus_data, colWidths=[4*inch, 1.5*inch])
        processus_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(processus_table)

        # Logique différente selon le template
        if export_request.template == "standard":
            # RAPPORT STANDARD : Ajouter le détail des risques
            story.append(PageBreak())
            story.append(Paragraph("Détail des Risques", heading_style))

            # Boucle des risques pour rapport standard
            for i, risque in enumerate(risques):
                # Titre du risque
                story.append(Paragraph(f"Risque #{i+1}: {risque.processus}", styles['Heading3']))

                # Tableau des détails
                details_data = [
                    ["Champ", "Valeur"],
                    ["Description", risque.description[:100] + "..." if len(risque.description) > 100 else risque.description],
                    ["Causes", risque.causes[:100] + "..." if risque.causes and len(risque.causes) > 100 else (risque.causes or "N/A")],
                    ["Effets", risque.effets[:100] + "..." if risque.effets and len(risque.effets) > 100 else (risque.effets or "N/A")],
                    ["Note Détection", str(risque.note_detection)],
                    ["Note Gravité", str(risque.note_gravite)],
                    ["Note Fréquence", str(risque.note_frequence)],
                    ["IPR", str(risque.ipr)],
                    ["Niveau de Criticité", risque.niveau_criticite],
                    ["Actions", risque.actions[:100] + "..." if risque.actions and len(risque.actions) > 100 else (risque.actions or "N/A")],
                    ["Responsables", risque.responsables or "N/A"]
                ]

                details_table = Table(details_data, colWidths=[2*inch, 4*inch])
                details_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                    ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP')
                ]))

                story.append(details_table)
                story.append(Spacer(1, 15))

        else:
            # RÉSUMÉ EXÉCUTIF : Ajouter seulement une conclusion
            story.append(Spacer(1, 20))
            story.append(Paragraph("Conclusion Exécutive", heading_style))

            conclusion_text = f"""
            Ce résumé exécutif présente une vue d'ensemble des {total_risques} risques identifiés dans l'organisation.

            Points clés :
            • {risques_critiques} risques critiques nécessitent une attention immédiate
            • {risques_mineurs} risques mineurs à surveiller
            • {risques_tolerables} risques tolérables sous contrôle
            • IPR moyen de {ipr_moyen:.1f} indique le niveau de risque global

            Recommandations :
            • Prioriser le traitement des risques critiques
            • Mettre en place un plan d'action pour les risques majeurs
            • Maintenir la surveillance des risques mineurs
            • Réviser périodiquement l'évaluation des risques
            """

            story.append(Paragraph(conclusion_text, styles['Normal']))

        # Construire le PDF
        doc.build(story)
        
        return filename
    
    @staticmethod
    def export_risques_word(
        db: Session, 
        export_request: ExportRequest,
        filename: Optional[str] = None
    ) -> str:
        """Exporte les risques en format Word"""
        
        if not filename:
            filename = f"rapport_risques_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        
        filepath = os.path.join("static", filename)
        
        # Créer le répertoire static s'il n'existe pas
        os.makedirs("static", exist_ok=True)
        
        # Récupérer les données filtrées
        risques = ExportService._get_filtered_risques(db, export_request)
        
        # Créer le document Word
        doc = Document()
        
        # Titre du rapport selon le template
        if export_request.template == "summary":
            titre = 'Résumé Exécutif - Analyse des Risques'
        else:
            titre = 'Rapport Complet d\'Analyse des Risques'

        title = doc.add_heading(titre, 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Type de rapport
        type_para = doc.add_paragraph(f'Type: {export_request.template.title()}')
        type_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Date de génération
        date_para = doc.add_paragraph(f'Généré le {datetime.now().strftime("%d/%m/%Y à %H:%M")}')
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        doc.add_page_break()
        
        # Résumé exécutif
        doc.add_heading('Résumé Exécutif', level=1)
        
        # Statistiques générales
        total_risques = len(risques)
        risques_critiques = len([r for r in risques if "Critique" in r.niveau_criticite])
        risques_mineurs = len([r for r in risques if "Mineur" in r.niveau_criticite])
        risques_tolerables = len([r for r in risques if "Tolérable" in r.niveau_criticite])
        ipr_moyen = sum([r.ipr for r in risques]) / total_risques if total_risques > 0 else 0
        
        # Tableau de résumé
        summary_table = doc.add_table(rows=6, cols=2)
        summary_table.style = 'Table Grid'
        summary_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        summary_data = [
            ("Métrique", "Valeur"),
            ("Nombre total de risques", str(total_risques)),
            ("Risques critiques", str(risques_critiques)),
            ("Risques mineurs", str(risques_mineurs)),
            ("Risques tolérables", str(risques_tolerables)),
            ("IPR moyen", f"{ipr_moyen:.2f}")
        ]
        
        for i, (metric, value) in enumerate(summary_data):
            row_cells = summary_table.rows[i].cells
            row_cells[0].text = metric
            row_cells[1].text = value
            if i == 0:  # En-tête
                for cell in row_cells:
                    cell.paragraphs[0].runs[0].bold = True
        
        doc.add_paragraph()
        
        # Distribution par processus
        doc.add_heading('Distribution par Processus', level=1)
        
        processus_stats = {}
        for risque in risques:
            if risque.processus not in processus_stats:
                processus_stats[risque.processus] = 0
            processus_stats[risque.processus] += 1
        
        processus_table = doc.add_table(rows=len(processus_stats)+1, cols=2)
        processus_table.style = 'Table Grid'
        
        # En-tête
        header_cells = processus_table.rows[0].cells
        header_cells[0].text = "Processus"
        header_cells[1].text = "Nombre de Risques"
        for cell in header_cells:
            cell.paragraphs[0].runs[0].bold = True
        
        # Données
        for i, (processus, count) in enumerate(sorted(processus_stats.items()), 1):
            row_cells = processus_table.rows[i].cells
            row_cells[0].text = processus
            row_cells[1].text = str(count)
        
        # Logique différente selon le template
        if export_request.template == "standard":
            # RAPPORT STANDARD : Ajouter le détail des risques
            doc.add_page_break()
            doc.add_heading('Détail des Risques', level=1)

            for i, risque in enumerate(risques):
                # Titre du risque
                doc.add_heading(f'Risque #{i+1}: {risque.processus}', level=2)

                # Tableau des détails
                details_table = doc.add_table(rows=11, cols=2)
                details_table.style = 'Table Grid'

                details_data = [
                    ("Champ", "Valeur"),
                    ("Description", risque.description),
                    ("Causes", risque.causes or "N/A"),
                    ("Effets", risque.effets or "N/A"),
                    ("Note Détection", str(risque.note_detection)),
                    ("Note Gravité", str(risque.note_gravite)),
                    ("Note Fréquence", str(risque.note_frequence)),
                    ("IPR", str(risque.ipr)),
                    ("Niveau de Criticité", risque.niveau_criticite),
                    ("Actions", risque.actions or "N/A"),
                    ("Responsables", risque.responsables or "N/A")
                ]

                for j, (field, value) in enumerate(details_data):
                    row_cells = details_table.rows[j].cells
                    row_cells[0].text = field
                    row_cells[1].text = value
                    if j == 0:  # En-tête
                        for cell in row_cells:
                            cell.paragraphs[0].runs[0].bold = True

                doc.add_paragraph()

        else:
            # RÉSUMÉ EXÉCUTIF : Ajouter seulement une conclusion
            doc.add_heading('Conclusion Exécutive', level=1)

            # Calculer les statistiques pour la conclusion
            total_risques = len(risques)
            risques_critiques = len([r for r in risques if 'critique' in (r.niveau_criticite or '').lower()])
            risques_mineurs = len([r for r in risques if 'mineur' in (r.niveau_criticite or '').lower()])
            risques_tolerables = total_risques - risques_critiques - risques_mineurs
            ipr_moyen = sum(r.ipr for r in risques) / total_risques if total_risques > 0 else 0

            conclusion_text = f"""Ce résumé exécutif présente une vue d'ensemble des {total_risques} risques identifiés dans l'organisation.

Points clés :
• {risques_critiques} risques critiques nécessitent une attention immédiate
• {risques_mineurs} risques mineurs à surveiller
• {risques_tolerables} risques tolérables sous contrôle
• IPR moyen de {ipr_moyen:.1f} indique le niveau de risque global

Recommandations :
• Prioriser le traitement des risques critiques
• Mettre en place un plan d'action pour les risques majeurs
• Maintenir la surveillance des risques mineurs
• Réviser périodiquement l'évaluation des risques"""

            doc.add_paragraph(conclusion_text)

        # Sauvegarder le document
        doc.save(filepath)
        
        return filename
    
    @staticmethod
    def _get_filtered_risques(db: Session, export_request: ExportRequest) -> List[Risque]:
        """Récupère les risques filtrés selon les critères d'export"""
        
        query = db.query(Risque)
        
        # Filtrer par processus
        if export_request.processus_filter:
            query = query.filter(Risque.processus.in_(export_request.processus_filter))
        
        # Filtrer par criticité
        if export_request.criticite_filter:
            query = query.filter(Risque.niveau_criticite.in_(export_request.criticite_filter))
        
        # Filtrer par date
        if export_request.date_debut:
            query = query.filter(Risque.created_at >= export_request.date_debut)
        
        if export_request.date_fin:
            query = query.filter(Risque.created_at <= export_request.date_fin)
        
        return query.all()
    
    @staticmethod
    def get_export_url(filename: str) -> str:
        """Génère l'URL de téléchargement pour un fichier exporté"""
        return f"/static/{filename}"
