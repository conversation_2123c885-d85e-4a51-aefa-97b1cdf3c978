#!/usr/bin/env python3
"""
Script de démarrage pour l'application Gestion des Risques
Démarre le backend et le frontend ensemble
"""

import subprocess
import time
import webbrowser
import sys
import os
import signal
import threading
from pathlib import Path

def print_banner():
    print("=" * 60)
    print("    APPLICATION GESTION DES RISQUES")
    print("=" * 60)
    print()

def check_python():
    """Vérifier que Python est disponible"""
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ requis")
            return False
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} détecté")
        return True
    except Exception:
        print("❌ Erreur lors de la vérification de Python")
        return False

def start_backend():
    """Démarrer le serveur backend"""
    print("🚀 Démarrage du backend...")
    
    try:
        # Démarrer le backend
        backend_process = subprocess.Popen([
            sys.executable, "start_api_simple.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre que le backend soit prêt
        print("⏳ Attente du démarrage du backend...")
        time.sleep(5)
        
        # Vérifier si le backend est accessible
        try:
            import requests
            response = requests.get("http://127.0.0.1:8000/api/risques", timeout=5)
            if response.status_code == 200:
                print("✅ Backend démarré avec succès sur http://127.0.0.1:8000")
                return backend_process
            else:
                print(f"❌ Backend répond avec le code {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Erreur de connexion au backend: {e}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lors du démarrage du backend: {e}")
        return None

def start_frontend():
    """Démarrer le serveur frontend"""
    print("🌐 Démarrage du frontend...")
    
    try:
        # Démarrer le frontend
        frontend_process = subprocess.Popen([
            sys.executable, "serve_frontend.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre que le frontend soit prêt
        print("⏳ Attente du démarrage du frontend...")
        time.sleep(3)
        
        print("✅ Frontend démarré avec succès sur http://localhost:3000")
        return frontend_process
        
    except Exception as e:
        print(f"❌ Erreur lors du démarrage du frontend: {e}")
        return None

def open_browser_delayed():
    """Ouvrir le navigateur après un délai"""
    time.sleep(2)
    print("🌍 Ouverture du navigateur...")
    webbrowser.open("http://localhost:3000")

def main():
    """Fonction principale"""
    print_banner()
    
    # Vérifications préliminaires
    if not check_python():
        input("Appuyez sur Entrée pour quitter...")
        return
    
    # Variables pour les processus
    backend_process = None
    frontend_process = None
    
    try:
        # Démarrer le backend
        backend_process = start_backend()
        if not backend_process:
            print("❌ Impossible de démarrer le backend")
            return
        
        # Démarrer le frontend
        frontend_process = start_frontend()
        if not frontend_process:
            print("❌ Impossible de démarrer le frontend")
            return
        
        # Ouvrir le navigateur
        threading.Thread(target=open_browser_delayed, daemon=True).start()
        
        print()
        print("=" * 60)
        print("    APPLICATION DÉMARRÉE AVEC SUCCÈS")
        print("=" * 60)
        print()
        print("📊 Services disponibles:")
        print("   • Interface Web:    http://localhost:3000")
        print("   • API Backend:      http://127.0.0.1:8000")
        print("   • Documentation:    http://127.0.0.1:8000/docs")
        print()
        print("🎯 Nouvelles fonctionnalités:")
        print("   • Llama IA Avancée (bouton flottant)")
        print("   • Méthode de Cotation Officielle")
        print("   • Prédictions Hybrides ML + LLM")
        print()
        print("⚠️  Pour arrêter l'application, appuyez sur Ctrl+C")
        print("=" * 60)
        
        # Attendre l'interruption
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'application...")
            
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        
    finally:
        # Nettoyer les processus
        if backend_process:
            print("🔄 Arrêt du backend...")
            backend_process.terminate()
            backend_process.wait()
            
        if frontend_process:
            print("🔄 Arrêt du frontend...")
            frontend_process.terminate()
            frontend_process.wait()
            
        print("✅ Application arrêtée proprement")

if __name__ == "__main__":
    main()
