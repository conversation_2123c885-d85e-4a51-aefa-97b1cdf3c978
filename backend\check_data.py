from app.models.database import engine
from app.models.risque import Risque
from sqlalchemy.orm import sessionmaker

Session = sessionmaker(bind=engine)
db = Session()

try:
    risques = db.query(Risque).all()
    print(f"Total des risques: {len(risques)}")
    print("\nRecherche de lignes suspectes:")
    
    suspects = []
    for r in risques:
        # Vérifier les lignes d'en-têtes ou données invalides
        if (r.description and ('Processus' in r.description or 'Risque' in r.description or 
                               'Source' in r.description or len(r.description.strip()) < 10)):
            suspects.append(r)
            print(f"ID {r.id}: {r.processus} - {r.description}")
    
    print(f"\nNombre de lignes suspectes: {len(suspects)}")
    
    # Vérifier la distribution par processus
    print("\nDistribution par processus:")
    processus_count = {}
    for r in risques:
        if r.processus:
            processus_count[r.processus] = processus_count.get(r.processus, 0) + 1
    
    for processus, count in sorted(processus_count.items()):
        print(f"  {processus}: {count} risques")
    
    # Vérifier s'il y a des doublons
    print("\nRecherche de doublons:")
    descriptions = {}
    for r in risques:
        if r.description:
            desc_key = r.description.strip().lower()
            if desc_key in descriptions:
                print(f"Doublon trouvé: ID {r.id} et ID {descriptions[desc_key]} - {r.description[:50]}...")
            else:
                descriptions[desc_key] = r.id

finally:
    db.close()
