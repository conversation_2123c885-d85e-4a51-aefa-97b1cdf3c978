import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, f1_score
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
# Import conditionnel pour éviter les erreurs si les packages ne sont pas installés
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost non disponible")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("⚠️ LightGBM non disponible")
import joblib
import os
import re
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class AdvancedRiskPredictor:
    """
    Prédicteur de risques avancé avec Gradient Boosting, XGBoost, LightGBM
    et analyse textuelle sophistiquée
    """
    
    def __init__(self):
        self.models = {}
        self.ensemble_model = None
        self.vectorizers = {}
        self.scalers = {}
        self.label_encoder = None
        self.feature_names = []
        self.is_trained = False
        self.model_path = "backend/app/ml/models/advanced"
        self.performance_metrics = {}
        
        # Créer le répertoire des modèles
        os.makedirs(self.model_path, exist_ok=True)
        
        # Dictionnaires de mots-clés étendus
        self.critical_keywords = {
            'urgence': ['urgent', 'critique', 'immédiat', 'prioritaire', 'grave'],
            'impact': ['catastrophique', 'majeur', 'sévère', 'important', 'significatif'],
            'technique': ['panne', 'défaillance', 'arrêt', 'dysfonctionnement', 'bug'],
            'financier': ['perte', 'coût', 'budget', 'rentabilité', 'investissement'],
            'humain': ['sécurité', 'santé', 'accident', 'blessure', 'formation'],
            'operationnel': ['retard', 'délai', 'qualité', 'performance', 'efficacité'],
            'reglementaire': ['conformité', 'audit', 'réglementation', 'norme', 'légal'],
            'reputation': ['image', 'réputation', 'client', 'satisfaction', 'confiance']
        }
        
        # Mots indicateurs de faible criticité
        self.low_risk_keywords = [
            'formation', 'amélioration', 'optimisation', 'suggestion', 
            'recommandation', 'préventif', 'maintenance', 'routine'
        ]
    
    def extract_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extraction de features avancées"""
        features_df = df.copy()
        
        # 1. Features textuelles de base
        features_df['description'] = features_df['description'].fillna('')
        features_df['causes'] = features_df['causes'].fillna('')
        features_df['effets'] = features_df['effets'].fillna('')
        
        # Texte combiné
        features_df['text_combined'] = (
            features_df['description'] + ' ' + 
            features_df['causes'] + ' ' + 
            features_df['effets']
        ).str.lower()
        
        # 2. Features de longueur et complexité
        features_df['text_length'] = features_df['text_combined'].str.len()
        features_df['word_count'] = features_df['text_combined'].str.split().str.len()
        features_df['sentence_count'] = features_df['text_combined'].str.count(r'[.!?]+') + 1
        features_df['avg_word_length'] = features_df['text_combined'].apply(
            lambda x: np.mean([len(word) for word in x.split()]) if x.split() else 0
        )
        
        # 3. Features de mots-clés par catégorie
        for category, keywords in self.critical_keywords.items():
            features_df[f'{category}_keywords'] = features_df['text_combined'].apply(
                lambda x: sum(1 for keyword in keywords if keyword in x)
            )
        
        # 4. Features de sentiment et intensité
        features_df['exclamation_count'] = features_df['text_combined'].str.count('!')
        features_df['question_count'] = features_df['text_combined'].str.count(r'\?')
        features_df['caps_ratio'] = features_df['description'].apply(
            lambda x: sum(1 for c in str(x) if c.isupper()) / max(len(str(x)), 1)
        )
        
        # 5. Features de structure
        features_df['has_causes'] = (features_df['causes'].str.len() > 0).astype(int)
        features_df['has_effets'] = (features_df['effets'].str.len() > 0).astype(int)
        features_df['causes_length'] = features_df['causes'].str.len()
        features_df['effets_length'] = features_df['effets'].str.len()
        
        # 6. Features de processus
        if 'processus' in features_df.columns:
            processus_encoder = LabelEncoder()
            features_df['processus_encoded'] = processus_encoder.fit_transform(
                features_df['processus'].fillna('Unknown')
            )
        else:
            features_df['processus_encoded'] = 0
        
        # 7. Features de risque faible
        features_df['low_risk_indicators'] = features_df['text_combined'].apply(
            lambda x: sum(1 for keyword in self.low_risk_keywords if keyword in x)
        )
        
        # 8. Ratios et interactions
        features_df['keyword_density'] = (
            features_df[[col for col in features_df.columns if col.endswith('_keywords')]].sum(axis=1) /
            features_df['word_count'].replace(0, 1)
        )
        
        features_df['description_causes_ratio'] = (
            features_df['description'].str.len() / 
            (features_df['causes'].str.len() + 1)
        )
        
        return features_df
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prépare les données d'entraînement avec features avancées"""
        
        print("🔧 Extraction des features avancées...")
        features_df = self.extract_advanced_features(df)
        
        # Features textuelles avec TF-IDF
        print("📝 Vectorisation TF-IDF...")
        if 'tfidf' not in self.vectorizers:
            self.vectorizers['tfidf'] = TfidfVectorizer(
                max_features=1000,
                ngram_range=(1, 3),
                stop_words=None,
                min_df=2,
                max_df=0.8
            )
            tfidf_features = self.vectorizers['tfidf'].fit_transform(features_df['text_combined'])
        else:
            tfidf_features = self.vectorizers['tfidf'].transform(features_df['text_combined'])
        
        # Features numériques
        numeric_columns = [
            'note_detection', 'note_gravite', 'note_frequence', 'ipr',
            'text_length', 'word_count', 'sentence_count', 'avg_word_length',
            'exclamation_count', 'question_count', 'caps_ratio',
            'has_causes', 'has_effets', 'causes_length', 'effets_length',
            'processus_encoded', 'low_risk_indicators', 'keyword_density',
            'description_causes_ratio'
        ]
        
        # Ajouter les features de mots-clés par catégorie
        numeric_columns.extend([f'{cat}_keywords' for cat in self.critical_keywords.keys()])
        
        # Sélectionner les colonnes existantes
        available_columns = [col for col in numeric_columns if col in features_df.columns]
        numeric_features = features_df[available_columns].fillna(0).values
        
        # Normalisation des features numériques
        if 'numeric' not in self.scalers:
            self.scalers['numeric'] = StandardScaler()
            numeric_features = self.scalers['numeric'].fit_transform(numeric_features)
        else:
            numeric_features = self.scalers['numeric'].transform(numeric_features)
        
        # Combiner toutes les features
        X = np.hstack([tfidf_features.toarray(), numeric_features])
        
        # Encoder les labels
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            y = self.label_encoder.fit_transform(features_df['niveau_criticite'])
        else:
            y = self.label_encoder.transform(features_df['niveau_criticite'])
        
        print(f"✅ Features préparées: {X.shape[0]} échantillons, {X.shape[1]} features")
        return X, y
    
    def create_models(self) -> Dict[str, Any]:
        """Crée les différents modèles ML"""
        
        models = {
            'random_forest': RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),
            
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            ),
            

        }

        # Ajouter XGBoost si disponible
        if XGBOOST_AVAILABLE:
            models['xgboost'] = xgb.XGBClassifier(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                min_child_weight=2,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='mlogloss'
            )

        # Ajouter LightGBM si disponible
        if LIGHTGBM_AVAILABLE:
            models['lightgbm'] = lgb.LGBMClassifier(
                n_estimators=150,
                learning_rate=0.1,
                max_depth=8,
                min_child_samples=5,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                verbose=-1
            )

        print(f"📊 Modèles créés: {list(models.keys())}")
        return models

    def train(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Entraîne tous les modèles et crée un ensemble"""

        print("🚀 Début de l'entraînement avancé...")

        # Préparer les données
        X, y = self.prepare_training_data(df)

        # Diviser en train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        # Créer les modèles
        models = self.create_models()

        # Entraîner chaque modèle
        print("🔄 Entraînement des modèles individuels...")
        model_scores = {}

        for name, model in models.items():
            print(f"  📊 Entraînement {name}...")

            # Entraînement
            model.fit(X_train, y_train)

            # Évaluation
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred, average='weighted')

            # Cross-validation
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')

            model_scores[name] = {
                'accuracy': accuracy,
                'f1_score': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std()
            }

            # Sauvegarder le modèle
            self.models[name] = model

            print(f"    ✅ {name}: Accuracy={accuracy:.3f}, F1={f1:.3f}, CV={cv_scores.mean():.3f}±{cv_scores.std():.3f}")

        # Créer un ensemble (Voting Classifier)
        print("🎯 Création de l'ensemble...")

        # Sélectionner les 3 meilleurs modèles
        best_models = sorted(model_scores.items(), key=lambda x: x[1]['cv_mean'], reverse=True)[:3]

        ensemble_estimators = [(name, self.models[name]) for name, _ in best_models]

        self.ensemble_model = VotingClassifier(
            estimators=ensemble_estimators,
            voting='soft'
        )

        self.ensemble_model.fit(X_train, y_train)

        # Évaluer l'ensemble
        y_pred_ensemble = self.ensemble_model.predict(X_test)
        ensemble_accuracy = accuracy_score(y_test, y_pred_ensemble)
        ensemble_f1 = f1_score(y_test, y_pred_ensemble, average='weighted')

        print(f"🏆 Ensemble: Accuracy={ensemble_accuracy:.3f}, F1={ensemble_f1:.3f}")

        # Rapport détaillé
        class_names = self.label_encoder.classes_
        ensemble_report = classification_report(
            y_test, y_pred_ensemble,
            target_names=class_names,
            output_dict=True
        )

        # Sauvegarder tout
        self.save_models()
        self.is_trained = True

        # Métriques de performance
        self.performance_metrics = {
            'individual_models': model_scores,
            'ensemble_accuracy': ensemble_accuracy,
            'ensemble_f1': ensemble_f1,
            'classification_report': ensemble_report,
            'best_models': [name for name, _ in best_models],
            'n_samples': len(df),
            'n_features': X.shape[1],
            'classes': class_names.tolist()
        }

        return self.performance_metrics

    def predict(self, description: str, causes: str = None, effets: str = None,
                processus: str = None, use_ensemble: bool = True) -> Dict[str, Any]:
        """Prédiction avancée avec analyse détaillée"""

        if not self.is_trained and not self.load_models():
            raise ValueError("Les modèles ne sont pas entraînés.")

        # Créer un DataFrame temporaire
        temp_df = pd.DataFrame({
            'description': [description],
            'causes': [causes or ''],
            'effets': [effets or ''],
            'processus': [processus or 'Unknown'],
            'note_detection': [2],
            'note_gravite': [2],
            'note_frequence': [2],
            'ipr': [8],
            'niveau_criticite': ['Tolérable (IPR < 8)']  # Dummy
        })

        # Extraire les features
        features_df = self.extract_advanced_features(temp_df)

        # Préparer pour la prédiction
        tfidf_features = self.vectorizers['tfidf'].transform(features_df['text_combined'])

        numeric_columns = [
            'note_detection', 'note_gravite', 'note_frequence', 'ipr',
            'text_length', 'word_count', 'sentence_count', 'avg_word_length',
            'exclamation_count', 'question_count', 'caps_ratio',
            'has_causes', 'has_effets', 'causes_length', 'effets_length',
            'processus_encoded', 'low_risk_indicators', 'keyword_density',
            'description_causes_ratio'
        ]
        numeric_columns.extend([f'{cat}_keywords' for cat in self.critical_keywords.keys()])

        available_columns = [col for col in numeric_columns if col in features_df.columns]
        numeric_features = features_df[available_columns].fillna(0).values
        numeric_features = self.scalers['numeric'].transform(numeric_features)

        X = np.hstack([tfidf_features.toarray(), numeric_features])

        # Prédiction
        if use_ensemble and self.ensemble_model:
            prediction = self.ensemble_model.predict(X)[0]
            probabilities = self.ensemble_model.predict_proba(X)[0]
            model_used = "Ensemble"
        else:
            # Utiliser le meilleur modèle individuel
            best_model_name = self.performance_metrics.get('best_models', ['random_forest'])[0]
            best_model = self.models[best_model_name]
            prediction = best_model.predict(X)[0]
            probabilities = best_model.predict_proba(X)[0]
            model_used = best_model_name

        predicted_class = self.label_encoder.inverse_transform([prediction])[0]
        confidence = float(max(probabilities))

        # Analyse des facteurs
        feature_analysis = self._analyze_prediction_factors(features_df.iloc[0])

        return {
            'niveau_predit': predicted_class,
            'confiance': confidence,
            'probabilites': {
                class_name: float(prob)
                for class_name, prob in zip(self.label_encoder.classes_, probabilities)
            },
            'facteurs_influents': feature_analysis,
            'model_used': model_used,
            'feature_importance': self._get_top_features(X[0])
        }

    def _analyze_prediction_factors(self, features_row) -> Dict[str, Any]:
        """Analyse détaillée des facteurs de prédiction"""

        factors = {
            'text_analysis': {
                'length': int(features_row.get('text_length', 0)),
                'word_count': int(features_row.get('word_count', 0)),
                'complexity': float(features_row.get('avg_word_length', 0))
            },
            'keyword_analysis': {},
            'structure_analysis': {
                'has_causes': bool(features_row.get('has_causes', False)),
                'has_effets': bool(features_row.get('has_effets', False)),
                'causes_detail': int(features_row.get('causes_length', 0)),
                'effets_detail': int(features_row.get('effets_length', 0))
            },
            'risk_indicators': {
                'low_risk_signals': int(features_row.get('low_risk_indicators', 0)),
                'keyword_density': float(features_row.get('keyword_density', 0))
            }
        }

        # Analyse par catégorie de mots-clés
        for category in self.critical_keywords.keys():
            col_name = f'{category}_keywords'
            if col_name in features_row:
                factors['keyword_analysis'][category] = int(features_row[col_name])

        return factors

    def _get_top_features(self, X_sample, top_n=5) -> List[Dict[str, Any]]:
        """Récupère les features les plus importantes pour cette prédiction"""

        if not hasattr(self.ensemble_model, 'feature_importances_'):
            # Pour l'ensemble, utiliser le premier modèle qui a feature_importances_
            for model_name, model in self.models.items():
                if hasattr(model, 'feature_importances_'):
                    importances = model.feature_importances_
                    break
            else:
                return []
        else:
            importances = self.ensemble_model.feature_importances_

        # Obtenir les indices des features les plus importantes
        top_indices = np.argsort(importances)[-top_n:][::-1]

        top_features = []
        for i, idx in enumerate(top_indices):
            if idx < len(importances):
                top_features.append({
                    'rank': i + 1,
                    'importance': float(importances[idx]),
                    'value': float(X_sample[idx]) if idx < len(X_sample) else 0.0
                })

        return top_features

    def save_models(self):
        """Sauvegarde tous les modèles et composants"""

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Sauvegarder les modèles individuels
        for name, model in self.models.items():
            model_file = os.path.join(self.model_path, f"{name}_{timestamp}.joblib")
            joblib.dump(model, model_file)

        # Sauvegarder l'ensemble
        if self.ensemble_model:
            ensemble_file = os.path.join(self.model_path, f"ensemble_{timestamp}.joblib")
            joblib.dump(self.ensemble_model, ensemble_file)

        # Sauvegarder les composants
        components = {
            'vectorizers': self.vectorizers,
            'scalers': self.scalers,
            'label_encoder': self.label_encoder,
            'performance_metrics': self.performance_metrics,
            'critical_keywords': self.critical_keywords,
            'low_risk_keywords': self.low_risk_keywords
        }

        components_file = os.path.join(self.model_path, f"components_{timestamp}.joblib")
        joblib.dump(components, components_file)

        # Sauvegarder les références des derniers modèles
        latest_refs = {
            'timestamp': timestamp,
            'models': {name: f"{name}_{timestamp}.joblib" for name in self.models.keys()},
            'ensemble': f"ensemble_{timestamp}.joblib",
            'components': f"components_{timestamp}.joblib"
        }

        refs_file = os.path.join(self.model_path, "latest_models.joblib")
        joblib.dump(latest_refs, refs_file)

        print(f"✅ Modèles sauvegardés avec timestamp: {timestamp}")

    def load_models(self) -> bool:
        """Charge les derniers modèles sauvegardés"""

        try:
            refs_file = os.path.join(self.model_path, "latest_models.joblib")
            if not os.path.exists(refs_file):
                print("❌ Aucun modèle sauvegardé trouvé")
                return False

            refs = joblib.load(refs_file)

            # Charger les composants
            components_file = os.path.join(self.model_path, refs['components'])
            components = joblib.load(components_file)

            self.vectorizers = components['vectorizers']
            self.scalers = components['scalers']
            self.label_encoder = components['label_encoder']
            self.performance_metrics = components['performance_metrics']
            self.critical_keywords = components['critical_keywords']
            self.low_risk_keywords = components['low_risk_keywords']

            # Charger les modèles individuels
            for name, filename in refs['models'].items():
                model_file = os.path.join(self.model_path, filename)
                self.models[name] = joblib.load(model_file)

            # Charger l'ensemble
            ensemble_file = os.path.join(self.model_path, refs['ensemble'])
            self.ensemble_model = joblib.load(ensemble_file)

            self.is_trained = True
            print(f"✅ Modèles chargés (timestamp: {refs['timestamp']})")
            return True

        except Exception as e:
            print(f"❌ Erreur lors du chargement des modèles: {e}")
            return False

    def get_model_info(self) -> Dict[str, Any]:
        """Retourne les informations sur les modèles"""

        if not self.is_trained:
            return {"status": "not_trained", "message": "Modèles non entraînés"}

        return {
            "status": "trained",
            "performance": self.performance_metrics,
            "models_available": list(self.models.keys()),
            "ensemble_available": self.ensemble_model is not None,
            "feature_categories": list(self.critical_keywords.keys()),
            "total_keywords": sum(len(keywords) for keywords in self.critical_keywords.values())
        }

    def retrain_if_needed(self, df: pd.DataFrame, min_accuracy_threshold: float = 0.85) -> bool:
        """Réentraîne automatiquement si les performances sont insuffisantes"""

        if not self.is_trained:
            print("🔄 Premier entraînement nécessaire...")
            self.train(df)
            return True

        current_accuracy = self.performance_metrics.get('ensemble_accuracy', 0)

        if current_accuracy < min_accuracy_threshold:
            print(f"🔄 Réentraînement nécessaire (accuracy actuelle: {current_accuracy:.3f})")
            self.train(df)
            return True

        print(f"✅ Modèle performant (accuracy: {current_accuracy:.3f})")
        return False
