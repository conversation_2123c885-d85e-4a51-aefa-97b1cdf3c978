from fastapi import APIRouter
from typing import Dict, List, Any

router = APIRouter(prefix="/api/methode-cotation", tags=["methode-cotation"])

@router.get("/echelles")
async def get_echelles_cotation():
    """Récupérer les échelles de cotation officielles"""
    return {
        "detection": {
            "titre": "Échelle de Détection",
            "description": "Capacité à détecter la défaillance avant qu'elle n'impacte le client",
            "echelle": [
                {
                    "note": 1,
                    "libelle": "Très facile à détecter",
                    "description": "La défaillance est identifiée rapidement par des contrôles réguliers ou des alertes automatiques",
                    "couleur": "green"
                },
                {
                    "note": 2,
                    "libelle": "Facile à détecter", 
                    "description": "La défaillance peut être remarquée lors d'inspections périodiques ou de vérifications",
                    "couleur": "yellow"
                },
                {
                    "note": 3,
                    "libelle": "Difficile à détecter",
                    "description": "La défaillance n'est détectée qu'occasionnellement ou nécessite des contrôles spécialisés",
                    "couleur": "orange"
                },
                {
                    "note": 4,
                    "libelle": "Quasi impossible à détecter",
                    "description": "La défaillance n'est généralement pas détectée avant d'impacter le client final",
                    "couleur": "red"
                }
            ]
        },
        "gravite": {
            "titre": "Échelle de Gravité",
            "description": "Impact de la défaillance sur le client, l'entreprise ou l'environnement",
            "echelle": [
                {
                    "note": 1,
                    "libelle": "Pas d'impact",
                    "description": "Aucun effet perceptible sur les performances, la sécurité ou la satisfaction client",
                    "couleur": "green"
                },
                {
                    "note": 2,
                    "libelle": "Impact léger",
                    "description": "Légère dégradation des performances ou gêne mineure pour le client",
                    "couleur": "yellow"
                },
                {
                    "note": 3,
                    "libelle": "Impact significatif",
                    "description": "Dégradation notable des performances, insatisfaction client ou coûts supplémentaires",
                    "couleur": "orange"
                },
                {
                    "note": 4,
                    "libelle": "Impact critique",
                    "description": "Arrêt de fonctionnement, danger pour la sécurité ou perte majeure de clientèle",
                    "couleur": "red"
                }
            ]
        },
        "frequence": {
            "titre": "Échelle de Fréquence",
            "description": "Probabilité d'occurrence de la défaillance",
            "echelle": [
                {
                    "note": 1,
                    "libelle": "Exceptionnel",
                    "description": "Très peu probable, moins d'une fois tous les 5 ans",
                    "couleur": "green"
                },
                {
                    "note": 2,
                    "libelle": "Rare",
                    "description": "Peu probable, une fois tous les 2-5 ans",
                    "couleur": "yellow"
                },
                {
                    "note": 3,
                    "libelle": "Peu fréquent",
                    "description": "Occasionnel, une fois par an environ",
                    "couleur": "orange"
                },
                {
                    "note": 4,
                    "libelle": "Fréquent",
                    "description": "Probable, plusieurs fois par an",
                    "couleur": "red"
                }
            ]
        }
    }

@router.get("/regles-criticite")
async def get_regles_criticite():
    """Récupérer les règles de classification de la criticité"""
    return {
        "formule": "IPR = Détection × Gravité × Fréquence",
        "plage_valeurs": {
            "minimum": 1,
            "maximum": 64
        },
        "niveaux": [
            {
                "niveau": "Tolérable",
                "condition": "IPR < 8",
                "description": "Risque acceptable en l'état actuel",
                "couleur": "green",
                "actions_recommandees": [
                    "Surveillance périodique",
                    "Maintien des mesures de contrôle existantes",
                    "Révision annuelle"
                ]
            },
            {
                "niveau": "Mineur", 
                "condition": "8 ≤ IPR ≤ 18",
                "description": "Risque nécessitant une surveillance renforcée",
                "couleur": "yellow",
                "actions_recommandees": [
                    "Mise en place de mesures de surveillance",
                    "Plan d'action préventif",
                    "Révision semestrielle",
                    "Formation du personnel concerné"
                ]
            },
            {
                "niveau": "Critique",
                "condition": "IPR > 18", 
                "description": "Risque nécessitant une action immédiate",
                "couleur": "red",
                "actions_recommandees": [
                    "Action corrective immédiate",
                    "Plan de contingence activé",
                    "Surveillance continue",
                    "Révision mensuelle",
                    "Escalade hiérarchique"
                ]
            }
        ]
    }

@router.post("/calculer-ipr")
async def calculer_ipr(detection: int, gravite: int, frequence: int):
    """Calculer l'IPR et déterminer le niveau de criticité"""
    
    # Validation des entrées
    if not all(1 <= val <= 4 for val in [detection, gravite, frequence]):
        return {
            "erreur": "Toutes les notes doivent être comprises entre 1 et 4"
        }
    
    # Calcul de l'IPR
    ipr = detection * gravite * frequence
    
    # Détermination du niveau de criticité
    if ipr < 8:
        niveau = "Tolérable"
        couleur = "green"
    elif 8 <= ipr <= 18:
        niveau = "Mineur"
        couleur = "yellow"
    else:
        niveau = "Critique"
        couleur = "red"
    
    return {
        "ipr": ipr,
        "niveau": niveau,
        "couleur": couleur,
        "details": {
            "detection": detection,
            "gravite": gravite,
            "frequence": frequence,
            "formule": f"{detection} × {gravite} × {frequence} = {ipr}"
        }
    }

@router.get("/exemples-calcul")
async def get_exemples_calcul():
    """Récupérer des exemples de calcul d'IPR"""
    return [
        {
            "titre": "Risque Tolérable - Erreur de saisie mineure",
            "detection": 2,
            "gravite": 1,
            "frequence": 3,
            "ipr": 6,
            "niveau": "Tolérable",
            "explication": "Erreur facilement détectable (2), sans impact (1), mais assez fréquente (3)"
        },
        {
            "titre": "Risque Mineur - Retard de livraison",
            "detection": 2,
            "gravite": 2,
            "frequence": 3,
            "ipr": 12,
            "niveau": "Mineur",
            "explication": "Retard détectable (2), impact modéré (2), fréquence occasionnelle (3)"
        },
        {
            "titre": "Risque Critique - Panne système critique",
            "detection": 3,
            "gravite": 4,
            "frequence": 2,
            "ipr": 24,
            "niveau": "Critique",
            "explication": "Difficile à détecter (3), impact majeur (4), rare mais possible (2)"
        }
    ]

@router.get("/matrice-risques")
async def get_matrice_risques():
    """Générer la matrice des risques pour visualisation"""
    matrice = []
    
    for detection in range(1, 5):
        for gravite in range(1, 5):
            for frequence in range(1, 5):
                ipr = detection * gravite * frequence
                
                if ipr < 8:
                    niveau = "Tolérable"
                    couleur = "green"
                elif 8 <= ipr <= 18:
                    niveau = "Mineur"
                    couleur = "yellow"
                else:
                    niveau = "Critique"
                    couleur = "red"
                
                matrice.append({
                    "detection": detection,
                    "gravite": gravite,
                    "frequence": frequence,
                    "ipr": ipr,
                    "niveau": niveau,
                    "couleur": couleur
                })
    
    return {
        "matrice": matrice,
        "statistiques": {
            "total_combinaisons": len(matrice),
            "tolerables": len([m for m in matrice if m["niveau"] == "Tolérable"]),
            "mineurs": len([m for m in matrice if m["niveau"] == "Mineur"]),
            "critiques": len([m for m in matrice if m["niveau"] == "Critique"])
        }
    }
