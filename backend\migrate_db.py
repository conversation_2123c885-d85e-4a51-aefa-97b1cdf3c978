import sqlite3
import os

def migrate_database():
    """
    Script de migration pour ajouter les colonnes manquantes à la base de données
    """
    print("Démarrage de la migration de la base de données...")
    
    # Chemin de la base de données
    db_path = os.path.join(os.path.dirname(__file__), "risques.db")
    
    if not os.path.exists(db_path):
        print(f"Erreur: Base de données non trouvée à {db_path}")
        return False
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Vérifier si la colonne actions_risques existe déjà
        cursor.execute("PRAGMA table_info(risques)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Liste des colonnes à ajouter
        columns_to_add = [
            ("actions_risques", "TEXT"),
            ("moyens_detection", "TEXT"),
            ("moyens_maitrise", "TEXT"),
            ("responsables", "TEXT"),
            ("delai_realisation", "TEXT"),
            ("methode_evaluation", "TEXT"),
            ("date_mesure", "TEXT"),
            ("evaluation_efficacite", "TEXT"),
            ("created_at", "DATETIME"),
            ("updated_at", "DATETIME")
        ]

        # Ajouter les colonnes manquantes
        for column_name, column_type in columns_to_add:
            if column_name not in columns:
                print(f"Ajout de la colonne '{column_name}'...")
                cursor.execute(f"ALTER TABLE risques ADD COLUMN {column_name} {column_type}")
        
        # Commit des changements
        conn.commit()
        print("Migration terminée avec succès!")
        return True
    
    except Exception as e:
        print(f"Erreur lors de la migration: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
