from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class NiveauCriticite(str, Enum):
    TOLERABLE = "Tolérable (IPR < 8)"
    MINEUR = "Mineur (8 ≤ IPR ≤ 18)"
    CRITIQUE = "Critique (IPR > 18)"

class ProcessusEnum(str, Enum):
    MANAGEMENT = "Management de l'entreprise"
    PRODUCTION = "Production"
    COMMERCIAL = "Commercial"
    RH = "Gestion des ressources humaines"
    IT = "Infrastrctures et IT"
    CUSTOMER_SERVICE = "Customer service"
    ACHAT = "Achat"
    FACTURATION = "Facturation et recouvrement"

# Schémas pour les Risques
class RisqueBase(BaseModel):
    processus: str = Field(..., min_length=1, max_length=100)
    source: Optional[str] = Field(None, max_length=100)
    enjeux: Optional[str] = None
    description: str = Field(..., min_length=1)
    causes: Optional[str] = None
    effets: Optional[str] = None
    moyens_detection: Optional[str] = None
    note_detection: int = Field(..., ge=1, le=4)
    note_gravite: int = Field(..., ge=1, le=4)
    note_frequence: int = Field(..., ge=1, le=4)
    moyens_maitrise: Optional[str] = None
    actions: Optional[str] = None
    responsables: Optional[str] = Field(None, max_length=200)
    delai: Optional[datetime] = None
    methode_evaluation: Optional[str] = None
    date_mesure: Optional[datetime] = None
    evaluation_efficacite: Optional[str] = None

class RisqueCreate(RisqueBase):
    pass

class RisqueUpdate(BaseModel):
    processus: Optional[str] = Field(None, min_length=1, max_length=100)
    source: Optional[str] = Field(None, max_length=100)
    enjeux: Optional[str] = None
    description: Optional[str] = Field(None, min_length=1)
    causes: Optional[str] = None
    effets: Optional[str] = None
    moyens_detection: Optional[str] = None
    note_detection: Optional[int] = Field(None, ge=1, le=4)
    note_gravite: Optional[int] = Field(None, ge=1, le=4)
    note_frequence: Optional[int] = Field(None, ge=1, le=4)
    moyens_maitrise: Optional[str] = None
    actions: Optional[str] = None
    responsables: Optional[str] = Field(None, max_length=200)
    delai: Optional[datetime] = None
    methode_evaluation: Optional[str] = None
    date_mesure: Optional[datetime] = None
    evaluation_efficacite: Optional[str] = None

class RisqueResponse(RisqueBase):
    id: int
    ipr: int
    niveau_criticite: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# Schémas pour les Opportunités
class OpportuniteBase(BaseModel):
    processus: str = Field(..., min_length=1, max_length=100)
    description: str = Field(..., min_length=1)
    impact_potentiel: Optional[str] = None
    faisabilite: Optional[int] = Field(None, ge=1, le=4)
    actions: Optional[str] = None
    responsables: Optional[str] = Field(None, max_length=200)
    delai: Optional[datetime] = None
    methode_evaluation: Optional[str] = None
    date_mesure: Optional[datetime] = None
    evaluation_efficacite: Optional[str] = None

class OpportuniteCreate(OpportuniteBase):
    pass

class OpportuniteUpdate(BaseModel):
    processus: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1)
    impact_potentiel: Optional[str] = None
    faisabilite: Optional[int] = Field(None, ge=1, le=4)
    actions: Optional[str] = None
    responsables: Optional[str] = Field(None, max_length=200)
    delai: Optional[datetime] = None
    methode_evaluation: Optional[str] = None
    date_mesure: Optional[datetime] = None
    evaluation_efficacite: Optional[str] = None

class OpportuniteResponse(OpportuniteBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

# Schémas pour les prédictions IA
class PredictionRequest(BaseModel):
    processus: str
    description: str
    causes: Optional[str] = None
    effets: Optional[str] = None

class PredictionResponse(BaseModel):
    niveau_predit: str
    confiance: float
    facteurs_influents: Dict[str, Any]
    probabilites: Optional[Dict[str, float]] = None
    model_used: Optional[str] = None
    feature_importance: Optional[List[Dict[str, Any]]] = None
    
    class Config:
        from_attributes = True

# Schémas pour les statistiques
class ProcessusStatsResponse(BaseModel):
    processus: str
    total_risques: int
    risques_tolerables: int
    risques_mineurs: int
    risques_critiques: int
    ipr_moyen: float
    
    class Config:
        from_attributes = True

class DashboardStats(BaseModel):
    total_risques: int
    total_opportunites: int
    risques_critiques: int
    risques_mineurs: int
    risques_tolerables: int
    ipr_moyen: float
    processus_stats: List[ProcessusStatsResponse]

class ChartData(BaseModel):
    labels: List[str]
    data: Union[List[int], List[List[int]], List[float]]
    colors: Optional[List[str]] = None

class DashboardCharts(BaseModel):
    distribution_criticite: ChartData
    distribution_processus: ChartData
    evolution_temporelle: Dict[str, Any]
    criticite_par_processus: ChartData
    ipr_moyen_par_processus: ChartData

# Schémas pour Llama IA
class LlamaPredictionRequest(BaseModel):
    processus: str
    description: str
    causes: Optional[str] = None
    effets: Optional[str] = None

class LlamaPredictionResponse(BaseModel):
    niveau: str
    confiance: float
    justification: str
    facteurs_cles: List[str]
    recommandations: List[str]
    ipr_estime: int
    model_used: Optional[str] = None

# Schémas pour l'export
class ExportRequest(BaseModel):
    format: str = Field(..., pattern="^(pdf|word)$")
    template: str = Field(default="standard", pattern="^(standard|summary)$")
    processus_filter: Optional[List[str]] = None
    criticite_filter: Optional[List[str]] = None
    date_debut: Optional[datetime] = None
    date_fin: Optional[datetime] = None
    include_opportunites: bool = False

class ExportResponse(BaseModel):
    filename: str
    download_url: str
    created_at: datetime

# Schémas pour les réponses paginées
class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int
