<!DOCTYPE html>
<html>
<head>
    <title>Test - Nouvelles Fonctionnalités</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .button { background: blue; color: white; padding: 10px; margin: 5px; border: none; cursor: pointer; }
        .section { display: none; margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .floating-btn { position: fixed; bottom: 20px; right: 20px; background: red; color: white; padding: 15px; border-radius: 50%; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>TEST - Nouvelles Fonctionnalités</h1>
    
    <div>
        <button class="button" onclick="showSection('dashboard')">Dashboard</button>
        <button class="button" onclick="showSection('llama')">Llama IA</button>
        <button class="button" onclick="showSection('methode')">Méthode de Cotation</button>
    </div>
    
    <div id="dashboard" class="section">
        <h2>Dashboard</h2>
        <p>Section Dashboard normale</p>
    </div>
    
    <div id="llama" class="section">
        <h2>Llama IA Avancée</h2>
        <p>Cette section devrait être visible quand on clique sur Llama IA</p>
        <button onclick="testAPI()">Tester API Llama</button>
        <div id="api-result"></div>
    </div>
    
    <div id="methode" class="section">
        <h2>Méthode de Cotation</h2>
        <p>Cette section devrait être visible quand on clique sur Méthode de Cotation</p>
        <button onclick="testMethodeAPI()">Tester API Méthode</button>
        <div id="methode-result"></div>
    </div>
    
    <button class="floating-btn" onclick="showSection('llama')">🤖</button>
    
    <script>
        function showSection(sectionId) {
            // Cacher toutes les sections
            document.querySelectorAll('.section').forEach(s => s.style.display = 'none');
            // Afficher la section demandée
            document.getElementById(sectionId).style.display = 'block';
            console.log('Section affichée:', sectionId);
        }
        
        async function testAPI() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/llama/status');
                const data = await response.json();
                document.getElementById('api-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('api-result').innerHTML = 'Erreur: ' + error.message;
            }
        }
        
        async function testMethodeAPI() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/methode-cotation/echelles');
                const data = await response.json();
                document.getElementById('methode-result').innerHTML = '<pre>' + JSON.stringify(data, null, 2).substring(0, 500) + '...</pre>';
            } catch (error) {
                document.getElementById('methode-result').innerHTML = 'Erreur: ' + error.message;
            }
        }
        
        // Afficher le dashboard par défaut
        showSection('dashboard');
    </script>
</body>
</html>
