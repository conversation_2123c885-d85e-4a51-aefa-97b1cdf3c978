import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
import joblib
import os
from typing import Dict, List, Tuple, Any
import re
from datetime import datetime

class RiskPredictor:
    """
    Modèle de Machine Learning pour prédire le niveau de criticité des risques
    """
    
    def __init__(self):
        self.model = None
        self.vectorizer = None
        self.label_encoder = None
        self.feature_names = []
        self.is_trained = False
        self.model_path = "backend/app/ml/models"
        
        # Créer le répertoire des modèles s'il n'existe pas
        os.makedirs(self.model_path, exist_ok=True)
    
    def preprocess_text(self, text: str) -> str:
        """Préprocesse le texte pour l'analyse"""
        if pd.isna(text) or text is None:
            return ""
        
        # Convertir en minuscules
        text = str(text).lower()
        
        # Supprimer les caractères spéciaux mais garder les espaces
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Supprimer les espaces multiples
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extrait les features pour l'entraînement"""
        
        # Créer une copie du dataframe
        features_df = df.copy()
        
        # Préprocesser les textes
        # Gérer les différents noms de colonnes
        desc_col = 'description' if 'description' in features_df.columns else 'risque'
        features_df['description_clean'] = features_df[desc_col].apply(self.preprocess_text)
        features_df['causes_clean'] = features_df['causes'].apply(self.preprocess_text)
        features_df['effets_clean'] = features_df['effets'].apply(self.preprocess_text)
        
        # Combiner tous les textes
        features_df['text_combined'] = (
            features_df['description_clean'] + ' ' + 
            features_df['causes_clean'] + ' ' + 
            features_df['effets_clean']
        )
        
        # Features numériques
        features_df['note_detection'] = pd.to_numeric(features_df['note_detection'], errors='coerce').fillna(1)
        features_df['note_gravite'] = pd.to_numeric(features_df['note_gravite'], errors='coerce').fillna(1)
        features_df['note_frequence'] = pd.to_numeric(features_df['note_frequence'], errors='coerce').fillna(1)
        features_df['ipr'] = pd.to_numeric(features_df['ipr'], errors='coerce').fillna(1)
        
        # Features dérivées
        features_df['text_length'] = features_df['text_combined'].str.len()
        features_df['description_length'] = features_df['description_clean'].str.len()
        features_df['has_causes'] = (features_df['causes_clean'].str.len() > 0).astype(int)
        features_df['has_effets'] = (features_df['effets_clean'].str.len() > 0).astype(int)
        
        # Mots-clés critiques
        critical_keywords = [
            'critique', 'urgent', 'grave', 'important', 'majeur', 'sévère',
            'perte', 'arrêt', 'échec', 'défaillance', 'problème', 'risque',
            'impact', 'conséquence', 'dommage', 'coût', 'retard'
        ]
        
        features_df['critical_keywords_count'] = features_df['text_combined'].apply(
            lambda x: sum(1 for keyword in critical_keywords if keyword in x)
        )
        
        # Encoder le processus
        if not hasattr(self, 'processus_encoder'):
            self.processus_encoder = LabelEncoder()
            features_df['processus_encoded'] = self.processus_encoder.fit_transform(features_df['processus'].fillna('Unknown'))
        else:
            # Pour les nouvelles prédictions
            processus_values = features_df['processus'].fillna('Unknown')
            # Gérer les nouvelles valeurs non vues pendant l'entraînement
            known_classes = set(self.processus_encoder.classes_)
            processus_values = processus_values.apply(
                lambda x: x if x in known_classes else 'Unknown'
            )
            features_df['processus_encoded'] = self.processus_encoder.transform(processus_values)
        
        return features_df
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prépare les données pour l'entraînement"""
        
        # Extraire les features
        features_df = self.extract_features(df)
        
        # Features textuelles
        if self.vectorizer is None:
            self.vectorizer = TfidfVectorizer(
                max_features=100,
                stop_words=None,  # Pas de stop words pour le français
                ngram_range=(1, 2),
                min_df=2
            )
            text_features = self.vectorizer.fit_transform(features_df['text_combined'])
        else:
            text_features = self.vectorizer.transform(features_df['text_combined'])
        
        # Features numériques
        numeric_features = features_df[[
            'note_detection', 'note_gravite', 'note_frequence', 'ipr',
            'text_length', 'description_length', 'has_causes', 'has_effets',
            'critical_keywords_count', 'processus_encoded'
        ]].values
        
        # Combiner les features
        X = np.hstack([text_features.toarray(), numeric_features])
        
        # Target
        if self.label_encoder is None:
            self.label_encoder = LabelEncoder()
            y = self.label_encoder.fit_transform(features_df['niveau_criticite'])
        else:
            y = self.label_encoder.transform(features_df['niveau_criticite'])
        
        return X, y
    
    def train(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Entraîne le modèle"""
        
        print("🤖 Début de l'entraînement du modèle...")
        
        # Préparer les données
        X, y = self.prepare_training_data(df)
        
        print(f"📊 Données d'entraînement: {X.shape[0]} échantillons, {X.shape[1]} features")
        
        # Diviser en train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Créer et entraîner le modèle
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            class_weight='balanced'
        )
        
        self.model.fit(X_train, y_train)
        
        # Évaluer le modèle
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"✅ Modèle entraîné avec succès!")
        print(f"📈 Précision: {accuracy:.3f}")
        
        # Rapport détaillé
        class_names = self.label_encoder.classes_
        report = classification_report(y_test, y_pred, target_names=class_names, output_dict=True)
        
        # Importance des features
        feature_importance = self.model.feature_importances_
        
        # Sauvegarder le modèle
        self.save_model()
        
        self.is_trained = True
        
        return {
            'accuracy': accuracy,
            'classification_report': report,
            'feature_importance': feature_importance.tolist(),
            'n_samples': len(df),
            'n_features': X.shape[1],
            'classes': class_names.tolist()
        }
    
    def predict(self, description: str, causes: str = None, effets: str = None, 
                processus: str = None) -> Dict[str, Any]:
        """Prédit le niveau de criticité d'un nouveau risque"""
        
        if not self.is_trained and not self.load_model():
            raise ValueError("Le modèle n'est pas entraîné. Veuillez d'abord entraîner le modèle.")
        
        # Créer un DataFrame temporaire
        temp_df = pd.DataFrame({
            'description': [description],
            'causes': [causes or ''],
            'effets': [effets or ''],
            'processus': [processus or 'Unknown'],
            'note_detection': [2],  # Valeurs par défaut
            'note_gravite': [2],
            'note_frequence': [2],
            'ipr': [8],
            'niveau_criticite': ['Tolérable (IPR < 8)']  # Dummy value
        })
        
        # Extraire les features
        features_df = self.extract_features(temp_df)
        
        # Préparer les features pour la prédiction
        text_features = self.vectorizer.transform(features_df['text_combined'])
        numeric_features = features_df[[
            'note_detection', 'note_gravite', 'note_frequence', 'ipr',
            'text_length', 'description_length', 'has_causes', 'has_effets',
            'critical_keywords_count', 'processus_encoded'
        ]].values
        
        X = np.hstack([text_features.toarray(), numeric_features])
        
        # Prédiction
        prediction = self.model.predict(X)[0]
        probabilities = self.model.predict_proba(X)[0]
        
        # Convertir en labels
        predicted_class = self.label_encoder.inverse_transform([prediction])[0]
        class_probabilities = {
            self.label_encoder.inverse_transform([i])[0]: prob 
            for i, prob in enumerate(probabilities)
        }
        
        # Facteurs influents (top features)
        feature_importance = self.model.feature_importances_
        top_features_idx = np.argsort(feature_importance)[-5:][::-1]
        
        return {
            'niveau_predit': predicted_class,
            'confiance': float(max(probabilities)),
            'probabilites': class_probabilities,
            'facteurs_influents': {
                'text_length': int(features_df['text_length'].iloc[0]),
                'critical_keywords': int(features_df['critical_keywords_count'].iloc[0]),
                'has_causes': bool(features_df['has_causes'].iloc[0]),
                'has_effets': bool(features_df['has_effets'].iloc[0]),
                'processus': processus or 'Unknown'
            }
        }
    
    def save_model(self):
        """Sauvegarde le modèle entraîné"""
        
        model_data = {
            'model': self.model,
            'vectorizer': self.vectorizer,
            'label_encoder': self.label_encoder,
            'processus_encoder': self.processus_encoder,
            'is_trained': True,
            'trained_at': datetime.now().isoformat()
        }
        
        model_file = os.path.join(self.model_path, 'risk_predictor.joblib')
        joblib.dump(model_data, model_file)
        print(f"💾 Modèle sauvegardé: {model_file}")
    
    def load_model(self) -> bool:
        """Charge un modèle pré-entraîné"""
        
        model_file = os.path.join(self.model_path, 'risk_predictor.joblib')
        
        if not os.path.exists(model_file):
            return False
        
        try:
            model_data = joblib.load(model_file)
            
            self.model = model_data['model']
            self.vectorizer = model_data['vectorizer']
            self.label_encoder = model_data['label_encoder']
            self.processus_encoder = model_data['processus_encoder']
            self.is_trained = model_data['is_trained']
            
            print(f"📂 Modèle chargé: {model_file}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement du modèle: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Retourne les informations sur le modèle"""
        
        if not self.is_trained:
            return {'status': 'not_trained'}
        
        return {
            'status': 'trained',
            'model_type': 'RandomForestClassifier',
            'n_features': len(self.vectorizer.get_feature_names_out()) + 10 if self.vectorizer else 0,
            'classes': self.label_encoder.classes_.tolist() if self.label_encoder else [],
            'is_loaded': self.model is not None
        }
