from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import Dict, Any
import httpx
import asyncio
import json
from ..models.database import get_db
from ..schemas import LlamaPredictionRequest, LlamaPredictionResponse

router = APIRouter(prefix="/api/llama", tags=["llama"])

OLLAMA_BASE_URL = "http://localhost:11434"

@router.get("/status")
async def check_llama_status():
    """Vérifier le statut d'Ollama et des modèles disponibles"""
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            # Vérifier si Ollama est en cours d'exécution
            try:
                response = await client.get(f"{OLLAMA_BASE_URL}/api/tags")
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    llama_models = [m for m in models if "llama" in m.get("name", "").lower()]
                    
                    if llama_models:
                        return {
                            "status": "available",
                            "message": "Ollama est disponible avec des modèles Llama",
                            "models": [m["name"] for m in llama_models],
                            "recommended_model": llama_models[0]["name"] if llama_models else None
                        }
                    else:
                        return {
                            "status": "no_models",
                            "message": "Ollama est disponible mais aucun modèle Llama n'est installé",
                            "models": [],
                            "install_command": "ollama pull llama3.2:3b"
                        }
                else:
                    return {
                        "status": "error",
                        "message": "Ollama ne répond pas correctement"
                    }
            except httpx.ConnectError:
                return {
                    "status": "not_running",
                    "message": "Ollama n'est pas en cours d'exécution",
                    "install_url": "https://ollama.ai/download"
                }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Erreur lors de la vérification: {str(e)}"
        }

@router.post("/predict", response_model=LlamaPredictionResponse)
async def predict_with_llama(request: LlamaPredictionRequest):
    """Prédire le niveau de risque avec Llama IA"""
    try:
        # Vérifier d'abord le statut
        status = await check_llama_status()
        if status["status"] != "available":
            raise HTTPException(
                status_code=503, 
                detail=f"Llama non disponible: {status['message']}"
            )
        
        # Construire le prompt pour Llama
        prompt = f"""Tu es un expert en gestion des risques. Analyse le risque suivant et détermine son niveau de criticité.

Processus: {request.processus}
Description: {request.description}
Causes: {request.causes or 'Non spécifiées'}
Effets: {request.effets or 'Non spécifiés'}

Règles de classification:
- Tolérable: IPR < 8 (risques mineurs, impact faible)
- Mineur: 8 ≤ IPR ≤ 18 (risques modérés, surveillance nécessaire)  
- Critique: IPR > 18 (risques majeurs, action immédiate requise)

Réponds UNIQUEMENT au format JSON suivant:
{{
    "niveau": "Tolérable|Mineur|Critique",
    "confiance": 0.85,
    "justification": "Explication détaillée de ton analyse",
    "facteurs_cles": ["facteur1", "facteur2", "facteur3"],
    "recommandations": ["recommandation1", "recommandation2"],
    "ipr_estime": 12
}}"""

        # Appeler Ollama
        async with httpx.AsyncClient(timeout=30.0) as client:
            ollama_request = {
                "model": status["recommended_model"],
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9
                }
            }
            
            response = await client.post(
                f"{OLLAMA_BASE_URL}/api/generate",
                json=ollama_request
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=500,
                    detail="Erreur lors de l'appel à Ollama"
                )
            
            result = response.json()
            llama_response = result.get("response", "")
            
            # Parser la réponse JSON de Llama
            try:
                # Extraire le JSON de la réponse
                start_idx = llama_response.find('{')
                end_idx = llama_response.rfind('}') + 1
                
                if start_idx != -1 and end_idx != -1:
                    json_str = llama_response[start_idx:end_idx]
                    parsed_result = json.loads(json_str)
                    
                    return LlamaPredictionResponse(
                        niveau=parsed_result.get("niveau", "Mineur"),
                        confiance=parsed_result.get("confiance", 0.7),
                        justification=parsed_result.get("justification", "Analyse basée sur les informations fournies"),
                        facteurs_cles=parsed_result.get("facteurs_cles", []),
                        recommandations=parsed_result.get("recommandations", []),
                        ipr_estime=parsed_result.get("ipr_estime", 10),
                        model_used=status["recommended_model"]
                    )
                else:
                    raise ValueError("Format JSON non trouvé dans la réponse")
                    
            except (json.JSONDecodeError, ValueError) as e:
                # Fallback: analyse simple basée sur les mots-clés
                niveau = "Mineur"
                confiance = 0.6
                
                description_lower = request.description.lower()
                if any(word in description_lower for word in ["critique", "majeur", "grave", "urgent", "catastrophique"]):
                    niveau = "Critique"
                    confiance = 0.8
                elif any(word in description_lower for word in ["mineur", "faible", "négligeable"]):
                    niveau = "Tolérable"
                    confiance = 0.7
                
                return LlamaPredictionResponse(
                    niveau=niveau,
                    confiance=confiance,
                    justification=f"Analyse de fallback basée sur les mots-clés. Réponse Llama: {llama_response[:200]}...",
                    facteurs_cles=["Analyse automatique"],
                    recommandations=["Vérifier manuellement l'analyse"],
                    ipr_estime=12 if niveau == "Mineur" else (20 if niveau == "Critique" else 5),
                    model_used=status["recommended_model"]
                )
                
    except httpx.TimeoutException:
        raise HTTPException(
            status_code=504,
            detail="Timeout lors de l'appel à Llama - le modèle met trop de temps à répondre"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la prédiction Llama: {str(e)}"
        )

@router.get("/examples")
async def get_llama_examples():
    """Récupérer des exemples optimisés pour Llama"""
    return [
        {
            "titre": "Cyberattaque Majeure",
            "processus": "Système d'information",
            "description": "Tentative d'intrusion dans le système informatique principal avec potentiel de vol de données clients et arrêt des services critiques",
            "causes": "Vulnérabilités non patchées, formation insuffisante du personnel, absence de surveillance 24/7",
            "effets": "Perte de données confidentielles, arrêt de production, atteinte à la réputation, sanctions réglementaires"
        },
        {
            "titre": "Changement Réglementaire",
            "processus": "Conformité réglementaire",
            "description": "Nouvelle réglementation européenne imposant des standards de qualité plus stricts avec délai de mise en conformité de 6 mois",
            "causes": "Évolution du cadre réglementaire, pression des autorités de contrôle, harmonisation européenne",
            "effets": "Coûts de mise en conformité élevés, risque d'amendes, nécessité de revoir les processus de production"
        },
        {
            "titre": "Turnover Critique",
            "processus": "Gestion des ressources humaines",
            "description": "Départ simultané de plusieurs experts clés dans un contexte de marché tendu avec difficultés de recrutement",
            "causes": "Conditions de travail dégradées, manque de perspectives d'évolution, offres concurrentielles attractives",
            "effets": "Perte de compétences critiques, surcharge des équipes restantes, retards dans les projets stratégiques"
        }
    ]
