from app.models.database import engine
from app.models.risque import Risque
from sqlalchemy.orm import sessionmaker

Session = sessionmaker(bind=engine)
db = Session()

try:
    # Récupérer tous les risques IT
    risques_it = db.query(Risque).filter(Risque.processus.like('%IT%')).all()
    print(f"Risques IT: {len(risques_it)}")
    print("=" * 60)
    
    for i, r in enumerate(risques_it):
        print(f"{i+1:2d}. ID {r.id:2d}: {r.description[:60]}...")
        if len(r.description) < 20:
            print(f"    ⚠️  Description courte: '{r.description}'")
        if not r.causes or len(r.causes.strip()) < 5:
            print(f"    ⚠️  Causes manquantes ou courtes")
        if not r.effets or len(r.effets.strip()) < 5:
            print(f"    ⚠️  Effets manquants ou courts")
    
    print("\n" + "=" * 60)
    print("Recherche de doublons dans IT:")
    descriptions = {}
    for r in risques_it:
        desc_key = r.description.strip().lower()
        if desc_key in descriptions:
            print(f"Doublon: ID {r.id} et ID {descriptions[desc_key]}")
            print(f"  - {r.description}")
        else:
            descriptions[desc_key] = r.id

finally:
    db.close()
