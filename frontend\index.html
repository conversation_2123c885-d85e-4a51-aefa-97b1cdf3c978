<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GESTION DES RISQUES</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <!-- Google Fonts pour une typographie professionnelle -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* Styles pour les graphiques */
        .chart-container {
            position: relative;
            transition: all 0.3s ease;
        }

        .chart-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Animation pour les cartes de statistiques */
        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        /* Styles pour les titres des graphiques */
        .chart-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Animation de chargement */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading {
            animation: pulse 2s infinite;
        }

        /* Animations pour le chatbot */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .chatbot-float {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes pulse-ring {
            0% { transform: scale(0.8); opacity: 1; }
            100% { transform: scale(2.5); opacity: 0; }
        }

        .pulse-ring {
            animation: pulse-ring 2s ease-out infinite;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-blue-600 text-white p-4">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <h1 class="text-2xl font-black tracking-wider" style="font-family: 'Inter', sans-serif; letter-spacing: 0.1em;">GESTION DES RISQUES</h1>
            </div>
            <div class="space-x-4">
                <button onclick="showDashboard()" class="hover:bg-blue-700 px-3 py-2 rounded">Dashboard</button>
                <button onclick="showRisques()" class="hover:bg-blue-700 px-3 py-2 rounded">Risques</button>
                <button onclick="showNewRisque()" class="hover:bg-blue-700 px-3 py-2 rounded">Nouveau Risque</button>
                <button onclick="showAdvancedPrediction()" class="hover:bg-blue-700 px-3 py-2 rounded">Prédiction IA</button>
                <button onclick="showMethodeCotation()" class="hover:bg-blue-700 px-3 py-2 rounded">Méthode de Cotation</button>
                <button id="export-btn" class="hover:bg-blue-700 px-3 py-2 rounded">Export</button>
            </div>
        </div>
    </nav>

    <!-- Dashboard Section -->
    <div id="dashboard" class="container mx-auto p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold">Dashboard des Risques</h2>
            <button onclick="loadDashboard(true)" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Actualiser
            </button>
        </div>
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 stat-card">
                <h3 class="text-lg font-semibold text-gray-600">Total Risques</h3>
                <p id="total-risques" class="text-3xl font-bold text-blue-600 loading">-</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 stat-card">
                <h3 class="text-lg font-semibold text-gray-600">Risques Critiques</h3>
                <p id="risques-critiques" class="text-3xl font-bold text-red-600 loading">-</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 stat-card">
                <h3 class="text-lg font-semibold text-gray-600">Risques Mineurs</h3>
                <p id="risques-mineurs" class="text-3xl font-bold text-yellow-600 loading">-</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 stat-card">
                <h3 class="text-lg font-semibold text-gray-600">Risques Tolérables</h3>
                <p id="risques-tolerables" class="text-3xl font-bold text-green-600 loading">-</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 stat-card">
                <h3 class="text-lg font-semibold text-gray-600">IPR Moyen</h3>
                <p id="ipr-moyen" class="text-3xl font-bold text-purple-600 loading">-</p>
            </div>
        </div>

        <!-- Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold chart-title">Distribution par Criticité</h3>
                    <div class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">Total: <span id="total-chart-label">-</span> risques</div>
                </div>
                <div class="relative" style="height: 350px;">
                    <canvas id="criticiteChart"></canvas>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold chart-title">Distribution par Processus</h3>
                    <div class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full"><span id="processus-count">-</span> processus</div>
                </div>
                <div class="relative" style="height: 350px;">
                    <canvas id="processusChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Nouveaux Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold chart-title">Criticité par Processus</h3>
                    <div class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">Répartition détaillée</div>
                </div>
                <div class="relative" style="height: 350px;">
                    <canvas id="criticiteProcessusChart"></canvas>
                </div>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200 chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold chart-title">IPR Moyen par Processus</h3>
                    <div class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">Évolution des risques</div>
                </div>
                <div class="relative" style="height: 350px;">
                    <canvas id="iprProcessusChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Chatbot Llama IA -->
        <div class="fixed bottom-6 right-6 z-50">
            <div class="relative">
                <!-- Bulle d'aide -->
                <div id="llama-tooltip" class="absolute bottom-16 right-0 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 transition-opacity duration-300 pointer-events-none">
                    Analyser avec Llama IA
                    <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                </div>

                <!-- Bouton Chatbot -->
                <button onclick="showLlama()"
                        onmouseenter="showLlamaTooltip()"
                        onmouseleave="hideLlamaTooltip()"
                        class="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-800 hover:from-blue-700 hover:to-blue-900 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group chatbot-float">
                    <!-- Icône Robot Assistant Risques -->
                    <svg class="w-8 h-8 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2M21 9V7L15 1H5C3.9 1 3 1.9 3 3V7C1.9 7 1 7.9 1 9V16C1 17.1 1.9 18 3 18V21C3 22.1 3.9 23 5 23H19C20.1 23 21 22.1 21 21V18C22.1 18 23 17.1 23 16V9C23 7.9 22.1 7 21 7V9M19 9V7H5V9H19M5 21V18H19V21H5M7.5 15.5C7.5 16.3 6.8 17 6 17S4.5 16.3 4.5 15.5 5.2 14 6 14 7.5 14.7 7.5 15.5M19.5 15.5C19.5 16.3 18.8 17 18 17S16.5 16.3 16.5 15.5 17.2 14 18 14 19.5 14.7 19.5 15.5M9 12H15V14H9V12Z"/>
                    </svg>

                    <!-- Indicateur de notification -->
                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                        <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    </div>
                </button>

                <!-- Texte sous le bouton -->
                <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 font-medium whitespace-nowrap">
                    Llama IA
                </div>
            </div>
        </div>
    </div>

    <!-- Risques List Section -->
    <div id="risques-list" class="container mx-auto p-6 hidden">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold">Liste des Risques</h2>
            <button onclick="loadRisques(true)" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Actualiser
            </button>
        </div>
        
        <!-- Filters -->
        <div class="bg-white p-4 rounded-lg shadow mb-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <select id="filter-processus" class="border rounded px-3 py-2">
                    <option value="">Tous les processus</option>
                </select>
                <select id="filter-criticite" class="border rounded px-3 py-2">
                    <option value="">Tous les niveaux</option>
                    <option value="Tolérable (IPR < 8)">Tolérable</option>
                    <option value="Mineur (8 ≤ IPR ≤ 18)">Mineur</option>
                    <option value="Critique (IPR > 18)">Critique</option>
                </select>
                <input id="search-input" type="text" placeholder="Rechercher..." class="border rounded px-3 py-2">
            </div>
            <div class="mt-4">
                <button onclick="filterRisques()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Filtrer
                </button>
            </div>
        </div>



        <!-- Liste des Risques -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Processus</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                            Risque
                            <div class="text-xs text-gray-400 normal-case mt-1">D=Détection, G=Gravité, F=Fréquence</div>
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">IPR</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Criticité</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                    </tr>
                </thead>
                <tbody id="risques-tbody" class="divide-y divide-gray-200">
                    <!-- Risques will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- New Risque Form -->
    <div id="new-risque" class="container mx-auto p-6 hidden">
        <h2 class="text-2xl font-bold mb-6">Nouveau Risque</h2>
        
        <form id="risque-form" class="bg-white p-6 rounded-lg shadow">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Processus *</label>
                    <select id="processus" name="processus" required class="w-full border rounded px-3 py-2">
                        <option value="">Sélectionner un processus</option>
                        <option value="Management de l'entreprise">Management de l'entreprise</option>
                        <option value="Production">Production</option>
                        <option value="Commercial">Commercial</option>
                        <option value="Gestion des ressources humaines">Gestion des ressources humaines</option>
                        <option value="Infrastructures et IT">Infrastructures et IT</option>
                        <option value="Customer service">Customer service</option>
                        <option value="Achat">Achat</option>
                        <option value="Facturation et recouvrement">Facturation et recouvrement</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Source</label>
                    <input type="text" id="source" name="source" class="w-full border rounded px-3 py-2" placeholder="Origine du risque">
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Enjeux (internes / externes)</label>
                    <textarea id="enjeux" name="enjeux" rows="2" class="w-full border rounded px-3 py-2" placeholder="Contexte et enjeux liés au risque"></textarea>
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description du Risque *</label>
                    <textarea id="description" name="description" required rows="3" class="w-full border rounded px-3 py-2"></textarea>
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Causes</label>
                    <textarea id="causes" name="causes" rows="2" class="w-full border rounded px-3 py-2"></textarea>
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Effets</label>
                    <textarea id="effets" name="effets" rows="2" class="w-full border rounded px-3 py-2"></textarea>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Moyen de Détection</label>
                    <textarea id="moyen_detection" name="moyen_detection" rows="2" class="w-full border rounded px-3 py-2" placeholder="Comment le risque est-il détecté ?"></textarea>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Moyen de Maîtrise</label>
                    <textarea id="moyen_maitrise" name="moyen_maitrise" rows="2" class="w-full border rounded px-3 py-2" placeholder="Mesures de contrôle et de prévention"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Note Détection (1-4) *</label>
                    <select id="note_detection" name="note_detection" required class="w-full border rounded px-3 py-2">
                        <option value="">Sélectionner</option>
                        <option value="1">1 - Très facile à détecter</option>
                        <option value="2">2 - Facile à détecter</option>
                        <option value="3">3 - Difficile à détecter</option>
                        <option value="4">4 - Quasi impossible à détecter</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Note Gravité (1-4) *</label>
                    <select id="note_gravite" name="note_gravite" required class="w-full border rounded px-3 py-2">
                        <option value="">Sélectionner</option>
                        <option value="1">1 - Pas d'impact</option>
                        <option value="2">2 - Impact léger</option>
                        <option value="3">3 - Impact significatif</option>
                        <option value="4">4 - Impact critique</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Note Fréquence (1-4) *</label>
                    <select id="note_frequence" name="note_frequence" required class="w-full border rounded px-3 py-2">
                        <option value="">Sélectionner</option>
                        <option value="1">1 - Exceptionnel</option>
                        <option value="2">2 - Rare</option>
                        <option value="3">3 - Peu fréquent</option>
                        <option value="4">4 - Fréquent</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">IPR Calculé</label>
                    <input type="text" id="ipr-display" readonly class="w-full border rounded px-3 py-2 bg-gray-100">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Niveau de Criticité</label>
                    <div class="flex space-x-2">
                        <button type="button" onclick="calculateRiskLevel()"
                                class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex-1">
                            <span>Calculer le Niveau</span>
                        </button>
                    </div>
                    <div id="risk-level-result" class="mt-2 hidden">
                        <div class="p-3 rounded border">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium">Niveau calculé:</span>
                                <span id="calculated-risk-level" class="px-2 py-1 rounded text-sm font-medium"></span>
                            </div>
                            <div class="text-xs text-gray-600 mt-1">
                                Basé sur IPR = Détection × Gravité × Fréquence
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Actions face aux Risques</label>
                    <textarea id="actions_risques" name="actions_risques" rows="2" class="w-full border rounded px-3 py-2" placeholder="Actions à mettre en place pour traiter le risque"></textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Responsables</label>
                    <input type="text" id="responsables" name="responsables" class="w-full border rounded px-3 py-2" placeholder="Personnes responsables du traitement">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Délai de Réalisation</label>
                    <input type="text" id="delai_realisation" name="delai_realisation" class="w-full border rounded px-3 py-2" placeholder="Ex: En continu, 3 mois, etc.">
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Méthode d'Évaluation de l'Efficacité</label>
                    <textarea id="methode_evaluation" name="methode_evaluation" rows="2" class="w-full border rounded px-3 py-2" placeholder="Comment mesurer l'efficacité des actions"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date de Mesure de l'Efficacité</label>
                    <input type="date" id="date_mesure" name="date_mesure" class="w-full border rounded px-3 py-2">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Évaluation de l'Efficacité</label>
                    <textarea id="evaluation_efficacite" name="evaluation_efficacite" rows="2" class="w-full border rounded px-3 py-2" placeholder="Résultats de l'évaluation"></textarea>
                </div>
            </div>
            
            <div class="mt-6 flex space-x-4">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                    Créer le Risque
                </button>
                <button type="button" onclick="showDashboard()" class="bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600">
                    Annuler
                </button>
            </div>
        </form>
    </div>

    <!-- Export Section -->
    <div id="export-section" class="container mx-auto p-6 hidden">
        <h2 class="text-2xl font-bold mb-6">Export des Rapports</h2>

        <div class="bg-white p-6 rounded-lg shadow">
            <form id="export-form">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Format d'export -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Format d'export *</label>
                        <select id="export-format" name="format" required class="w-full border rounded px-3 py-2">
                            <option value="">Sélectionner un format</option>
                            <option value="pdf">PDF</option>
                            <option value="word">Word (DOCX)</option>
                        </select>
                    </div>

                    <!-- Template -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Type de rapport</label>
                        <select id="export-template" class="w-full border rounded px-3 py-2" onchange="console.log('Template changé:', this.value)">
                            <option value="standard">Rapport Standard</option>
                            <option value="summary">Résumé Exécutif</option>
                        </select>
                    </div>

                    <!-- Filtres par processus -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Filtrer par processus</label>
                        <select id="export-processus" multiple class="w-full border rounded px-3 py-2" size="4">
                            <!-- Options will be loaded dynamically -->
                        </select>
                        <small class="text-gray-500">Maintenez Ctrl pour sélectionner plusieurs processus</small>
                    </div>

                    <!-- Filtres par criticité -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Filtrer par criticité</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" value="Tolérable (IPR < 8)" class="export-criticite mr-2">
                                <span>Risques Tolérables</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="Mineur (8 ≤ IPR ≤ 18)" class="export-criticite mr-2">
                                <span>Risques Mineurs</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" value="Critique (IPR > 18)" class="export-criticite mr-2">
                                <span>Risques Critiques</span>
                            </label>
                        </div>
                    </div>

                    <!-- Filtres par date -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date de début</label>
                        <input type="date" id="export-date-debut" class="w-full border rounded px-3 py-2">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date de fin</label>
                        <input type="date" id="export-date-fin" class="w-full border rounded px-3 py-2">
                    </div>
                </div>

                <div class="mt-6 flex space-x-4">
                    <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">
                        <span id="export-btn-text">Générer le Rapport</span>
                        <span id="export-loading" class="hidden">Génération en cours...</span>
                    </button>
                    <button type="button" onclick="showDashboard()" class="bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600">
                        Retour
                    </button>
                </div>
            </form>

            <!-- Zone de téléchargement -->
            <div id="download-section" class="mt-6 hidden">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-green-800 mb-2">Rapport généré avec succès!</h3>
                    <p class="text-green-700 mb-3">Votre rapport est prêt à être téléchargé.</p>
                    <a id="download-link" href="#" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 inline-block">
                        Télécharger le Rapport
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- IA Avancée Section -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">Analyse IA d'un Nouveau Risque</h3>

                <form id="prediction-form">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Processus</label>
                            <select id="pred-processus" class="w-full border rounded px-3 py-2">
                                <option value="">Sélectionner un processus</option>
                                <option value="Management de l'entreprise">Management de l'entreprise</option>
                                <option value="Production">Production</option>
                                <option value="Commercial">Commercial</option>
                                <option value="Gestion des ressources humaines">Gestion des ressources humaines</option>
                                <option value="Infrastructures et IT">Infrastructures et IT</option>
                                <option value="Customer service">Customer service</option>
                                <option value="Achat">Achat</option>
                                <option value="Facturation et recouvrement">Facturation et recouvrement</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description du Risque *</label>
                            <textarea id="pred-description" required rows="3"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Décrivez le risque de manière détaillée..."></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Causes</label>
                            <textarea id="pred-causes" rows="2"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Quelles sont les causes potentielles ?"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Effets</label>
                            <textarea id="pred-effets" rows="2"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Quels sont les effets possibles ?"></textarea>
                        </div>

                        <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            <span id="pred-btn-text">Analyser avec l'IA</span>
                            <span id="pred-loading" class="hidden">Analyse en cours...</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Résultats de prédiction -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">Résultats de l'Analyse IA</h3>

                <div id="prediction-results" class="hidden">
                    <!-- Niveau prédit -->
                    <div class="mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">Niveau de Criticité Prédit</h4>
                        <div id="predicted-level" class="text-2xl font-bold p-3 rounded text-center">
                            <!-- Sera rempli dynamiquement -->
                        </div>
                    </div>

                    <!-- Confiance -->
                    <div class="mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">Confiance de la Prédiction</h4>
                        <div class="bg-gray-200 rounded-full h-4">
                            <div id="confidence-bar" class="bg-blue-600 h-4 rounded-full transition-all duration-300"></div>
                        </div>
                        <p id="confidence-text" class="text-sm text-gray-600 mt-1"></p>
                    </div>

                    <!-- Facteurs influents -->
                    <div class="mb-4">
                        <h4 class="font-medium text-gray-700 mb-2">Facteurs d'Analyse</h4>
                        <div id="influence-factors" class="space-y-2">
                            <!-- Sera rempli dynamiquement -->
                        </div>
                    </div>

                    <!-- Actions recommandées -->
                    <div class="bg-blue-50 p-4 rounded">
                        <h4 class="font-medium text-blue-800 mb-2">💡 Recommandations</h4>
                        <div id="recommendations" class="text-sm text-blue-700">
                            <!-- Sera rempli dynamiquement -->
                        </div>
                    </div>
                </div>

                <div id="prediction-placeholder" class="text-center text-gray-500 py-8">
                    <div class="text-2xl font-bold mb-4">Prédiction IA</div>
                    <p>Remplissez le formulaire pour obtenir une prédiction IA du niveau de criticité</p>
                </div>
            </div>
        </div>

        <!-- Exemples de test -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4">Exemples de Test</h3>
            <p class="text-gray-600 mb-4">Testez l'IA avec ces exemples prédéfinis :</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="loadExample(0)" class="text-left p-4 border rounded hover:bg-gray-50">
                    <h4 class="font-medium text-red-600">Risque Critique</h4>
                    <p class="text-sm text-gray-600">Panne serveur production</p>
                </button>
                <button onclick="loadExample(1)" class="text-left p-4 border rounded hover:bg-gray-50">
                    <h4 class="font-medium text-yellow-600">Risque Mineur</h4>
                    <p class="text-sm text-gray-600">Erreur facturation</p>
                </button>
                <button onclick="loadExample(2)" class="text-left p-4 border rounded hover:bg-gray-50">
                    <h4 class="font-medium text-green-600">Risque Tolérable</h4>
                    <p class="text-sm text-gray-600">Formation personnel</p>
                </button>
            </div>
        </div>

        <!-- Informations sur le modèle -->
        <div class="mt-8 bg-gray-50 p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">ℹ️ À propos du Modèle IA</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium mb-2">Performance</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Précision: 90%</li>
                        <li>• Entraîné sur 99 risques réels</li>
                        <li>• 110 caractéristiques analysées</li>
                        <li>• Modèle: Random Forest</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Facteurs Analysés</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Vocabulaire et mots-clés critiques</li>
                        <li>• Longueur et détail des descriptions</li>
                        <li>• Type de processus concerné</li>
                        <li>• Présence de causes et effets</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Vérifier que Chart.js se charge
        window.addEventListener('load', function() {
            console.log('Chart.js disponible:', typeof Chart !== 'undefined');
            if (typeof Chart === 'undefined') {
                console.error('Chart.js non chargé !');
            }
        });
    </script>
    <script src="app.js"></script>

    <!-- Llama IA Section -->
    <div id="llama-section" class="container mx-auto p-6 hidden">
        <h2 class="text-2xl font-bold mb-6">Llama - IA Avancée</h2>

        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        <strong>Llama IA Avancée</strong> utilisant un Large Language Model pour une analyse contextuelle
                        approfondie avec justifications détaillées et recommandations personnalisées.
                    </p>
                </div>
            </div>
        </div>

        <!-- Statut Llama -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h3 class="text-lg font-semibold mb-4">Statut de Llama</h3>
            <div id="llama-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span>Vérification en cours...</span>
                </div>
            </div>
            <button onclick="checkLlamaStatus()" class="mt-4 bg-blue-700 text-white px-4 py-2 rounded hover:bg-blue-800">
                Vérifier le statut
            </button>
        </div>

        <!-- Formulaire Llama -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">Analyse Llama Avancée</h3>

                <form id="llama-form">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Processus</label>
                            <select id="llama-processus" class="w-full border rounded px-3 py-2">
                                <option value="">Sélectionner un processus</option>
                                <option value="Management de l'entreprise">Management de l'entreprise</option>
                                <option value="Production">Production</option>
                                <option value="Commercial">Commercial</option>
                                <option value="Gestion des ressources humaines">Gestion des ressources humaines</option>
                                <option value="Infrastructures et IT">Infrastructures et IT</option>
                                <option value="Customer service">Customer service</option>
                                <option value="Achat">Achat</option>
                                <option value="Facturation et recouvrement">Facturation et recouvrement</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description du Risque *</label>
                            <textarea id="llama-description" required rows="3"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Décrivez le risque de manière détaillée..."></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Causes</label>
                            <textarea id="llama-causes" rows="2"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Quelles sont les causes potentielles ?"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Effets</label>
                            <textarea id="llama-effets" rows="2"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Quels sont les effets possibles ?"></textarea>
                        </div>

                        <div class="flex space-x-2">
                            <button type="button" onclick="predictWithLlama()"
                                    class="bg-blue-700 text-white px-4 py-2 rounded hover:bg-blue-800 flex-1">
                                <span id="llama-btn-text">Analyser avec Llama</span>
                                <span id="llama-loading" class="hidden">Analyse en cours...</span>
                            </button>
                            <button type="button" onclick="hybridPredict()"
                                    class="bg-gradient-to-r from-blue-600 to-blue-800 text-white px-4 py-2 rounded hover:from-blue-700 hover:to-blue-900">
                                Hybride
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Résultats Llama -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">Résultats Llama</h3>

                <div id="llama-placeholder" class="text-center text-gray-500 py-8">
                    <div class="text-2xl font-bold mb-2">Llama IA</div>
                    <p>Utilisez le formulaire pour obtenir une analyse Llama détaillée</p>
                </div>

                <div id="llama-results" class="hidden">
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-700">Niveau Prédit:</label>
                            <div id="llama-predicted-level" class="mt-1"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Confiance:</label>
                            <div class="mt-1">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div id="llama-confidence-bar" class="bg-blue-700 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                                <span id="llama-confidence-text" class="text-sm text-gray-600"></span>
                            </div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Justification:</label>
                            <div id="llama-justification" class="mt-1 p-3 bg-gray-50 rounded text-sm"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Facteurs Clés:</label>
                            <div id="llama-factors" class="mt-1"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Recommandations:</label>
                            <div id="llama-recommendations" class="mt-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exemples Llama -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4">Exemples Optimisés pour Llama</h3>
            <p class="text-gray-600 mb-4">Testez Llama avec des cas complexes nécessitant une analyse contextuelle :</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="loadLlamaExample(0)" class="text-left p-4 border rounded hover:bg-blue-50">
                    <h4 class="font-medium text-red-600">Cyberattaque Majeure</h4>
                    <p class="text-sm text-gray-600">Analyse contextuelle complexe</p>
                </button>
                <button onclick="loadLlamaExample(1)" class="text-left p-4 border rounded hover:bg-blue-50">
                    <h4 class="font-medium text-yellow-600">Changement Réglementaire</h4>
                    <p class="text-sm text-gray-600">Impact multi-processus</p>
                </button>
                <button onclick="loadLlamaExample(2)" class="text-left p-4 border rounded hover:bg-blue-50">
                    <h4 class="font-medium text-blue-600">Turnover Critique</h4>
                    <p class="text-sm text-gray-600">Analyse nuancée RH</p>
                </button>
            </div>
        </div>

        <!-- Installation Ollama -->
        <div class="mt-8 bg-yellow-50 p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">⚙️ Installation d'Ollama</h3>
            <div id="ollama-instructions">
                <p class="text-sm text-gray-700 mb-4">
                    Pour utiliser Llama, vous devez installer Ollama sur votre système :
                </p>
                <ol class="list-decimal list-inside space-y-2 text-sm text-gray-700">
                    <li>Télécharger Ollama : <a href="https://ollama.ai/download" target="_blank" class="text-blue-600 hover:underline">https://ollama.ai/download</a></li>
                    <li>Installer Ollama sur votre système</li>
                    <li>Ouvrir un terminal et exécuter : <code class="bg-gray-200 px-2 py-1 rounded">ollama pull llama3.2:3b</code></li>
                    <li>Démarrer Ollama : <code class="bg-gray-200 px-2 py-1 rounded">ollama serve</code></li>
                    <li>Redémarrer cette application</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- IA Avancée Section -->
    <div id="advanced-prediction-section" class="container mx-auto p-6 hidden">
        <h2 class="text-2xl font-bold mb-6">Prédiction IA - Machine Learning Avancé</h2>

        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        <strong>Ensemble de modèles ML avancés</strong> : Random Forest, Gradient Boosting, XGBoost, LightGBM
                        avec features sophistiquées et analyse textuelle TF-IDF pour une précision maximale.
                    </p>
                </div>
            </div>
        </div>

        <!-- Statut des modèles -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h3 class="text-lg font-semibold mb-4">Statut des Modèles ML</h3>
            <div id="advanced-models-status">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span>Vérification en cours...</span>
                </div>
            </div>
            <div class="mt-4 space-x-2">
                <button onclick="checkAdvancedModelsStatus()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Vérifier le statut
                </button>
                <button onclick="trainAdvancedModels()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Entraîner les modèles
                </button>
                <button onclick="retrainAdvancedModels()" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                    Réentraîner
                </button>
            </div>
        </div>

        <!-- Prédiction avancée -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">Prédiction ML Avancée</h3>

                <form id="advanced-prediction-form">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Processus</label>
                            <select id="advanced-processus" class="w-full border rounded px-3 py-2">
                                <option value="">Sélectionner un processus</option>
                                <option value="Management de l'entreprise">Management de l'entreprise</option>
                                <option value="Production">Production</option>
                                <option value="Commercial">Commercial</option>
                                <option value="Gestion des ressources humaines">Gestion des ressources humaines</option>
                                <option value="Infrastructures et IT">Infrastructures et IT</option>
                                <option value="Customer service">Customer service</option>
                                <option value="Achat">Achat</option>
                                <option value="Facturation et recouvrement">Facturation et recouvrement</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description du Risque *</label>
                            <textarea id="advanced-description" required rows="3"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Décrivez le risque de manière détaillée..."></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Causes</label>
                            <textarea id="advanced-causes" rows="2"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Quelles sont les causes potentielles ?"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Effets</label>
                            <textarea id="advanced-effets" rows="2"
                                    class="w-full border rounded px-3 py-2"
                                    placeholder="Quels sont les effets possibles ?"></textarea>
                        </div>

                        <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            <span id="advanced-pred-btn-text">Analyser avec l'IA Avancée</span>
                            <span id="advanced-pred-loading" class="hidden">Analyse en cours...</span>
                        </button>
                    </div>
                </form>

                <!-- Exemples de Test -->
                <div class="mt-6">
                    <h4 class="text-md font-semibold mb-3">Exemples de Test</h4>
                    <div class="grid grid-cols-1 gap-2">
                        <button onclick="loadExample(0)" class="text-left p-3 bg-red-50 border border-red-200 rounded hover:bg-red-100 transition-colors">
                            <div class="font-medium text-red-800">Risque Critique</div>
                            <div class="text-sm text-red-600">Panne serveur principal</div>
                        </button>
                        <button onclick="loadExample(1)" class="text-left p-3 bg-yellow-50 border border-yellow-200 rounded hover:bg-yellow-100 transition-colors">
                            <div class="font-medium text-yellow-800">Risque Mineur</div>
                            <div class="text-sm text-yellow-600">Erreurs de facturation</div>
                        </button>
                        <button onclick="loadExample(2)" class="text-left p-3 bg-green-50 border border-green-200 rounded hover:bg-green-100 transition-colors">
                            <div class="font-medium text-green-800">Risque Tolérable</div>
                            <div class="text-sm text-green-600">Formation personnel</div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Résultats avancés -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">Résultats IA Avancée</h3>

                <div id="advanced-prediction-placeholder" class="text-center text-gray-500 py-8">
                    <div class="text-2xl font-bold mb-2">🤖 IA Avancée</div>
                    <p>Utilisez le formulaire ou les exemples pour obtenir une prédiction ML avancée</p>
                </div>

                <div id="advanced-prediction-results" class="hidden">
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-700">Niveau Prédit:</label>
                            <div id="advanced-predicted-level" class="mt-1"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Confiance:</label>
                            <div class="mt-1">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div id="advanced-confidence-bar" class="bg-purple-600 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                                <span id="advanced-confidence-text" class="text-sm text-gray-600"></span>
                            </div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Modèle Utilisé:</label>
                            <div id="advanced-model-used" class="mt-1 text-sm text-gray-600"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Probabilités:</label>
                            <div id="advanced-probabilities" class="mt-1"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Analyse des Features:</label>
                            <div id="advanced-feature-analysis" class="mt-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance des modèles -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4">Performance des Modèles</h3>
            <div id="models-performance">
                <p class="text-gray-500">Cliquez sur "Vérifier le statut" pour voir les performances</p>
            </div>
        </div>
    </div>

    <!-- Llama Section -->
    <div id="llama-section" class="container mx-auto p-6 hidden">
                    </div>
                </form>
            </div>

            <!-- Résultats avancés -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">Résultats IA Avancée</h3>

                <div id="advanced-prediction-placeholder" class="text-center text-gray-500 py-8">
                    <div class="text-2xl font-bold mb-2">🤖 IA Avancée</div>
                    <p>Utilisez le formulaire pour obtenir une prédiction ML avancée</p>
                </div>

                <div id="advanced-prediction-results" class="hidden">
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-700">Niveau Prédit:</label>
                            <div id="advanced-predicted-level" class="mt-1"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Confiance:</label>
                            <div class="mt-1">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div id="advanced-confidence-bar" class="bg-purple-600 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                                <span id="advanced-confidence-text" class="text-sm text-gray-600"></span>
                            </div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Modèle Utilisé:</label>
                            <div id="advanced-model-used" class="mt-1 text-sm text-gray-600"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Probabilités:</label>
                            <div id="advanced-probabilities" class="mt-1"></div>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-gray-700">Analyse des Features:</label>
                            <div id="advanced-feature-analysis" class="mt-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance des modèles -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4">Performance des Modèles</h3>
            <div id="models-performance">
                <p class="text-gray-500">Cliquez sur "Vérifier le statut" pour voir les performances</p>
            </div>
        </div>
    </div>

    <!-- Méthode de Cotation Section -->
    <div id="methode-cotation-section" class="container mx-auto p-6 hidden">
        <h2 class="text-2xl font-bold mb-6">Méthode de Cotation des Risques</h2>

        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        <strong>Méthode de cotation officielle</strong> extraite du fichier "Analyse des risques et opportunités.xls"
                        pour l'évaluation systématique des risques selon la méthode AMDEC.
                    </p>
                </div>
            </div>
        </div>

        <!-- Calcul IPR -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-xl font-semibold mb-4">Calcul de l'Indice de Priorité du Risque (IPR)</h3>
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 mb-2">IPR = Détection × Gravité × Fréquence</div>
                    <div class="text-gray-600">Valeurs possibles : 1 à 64</div>
                </div>
            </div>
        </div>

        <!-- Échelles de Cotation -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Détection -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-4 text-blue-600">Échelle de Détection</h3>
                <div class="space-y-3">
                    <div class="border-l-4 border-green-400 pl-3">
                        <div class="font-semibold">1 - Très facile à détecter</div>
                        <div class="text-sm text-gray-600">La défaillance est identifiée rapidement par des contrôles réguliers ou des alertes automatiques</div>
                    </div>
                    <div class="border-l-4 border-yellow-400 pl-3">
                        <div class="font-semibold">2 - Facile à détecter</div>
                        <div class="text-sm text-gray-600">La défaillance peut être remarquée lors d'inspections périodiques ou de vérifications</div>
                    </div>
                    <div class="border-l-4 border-orange-400 pl-3">
                        <div class="font-semibold">3 - Difficile à détecter</div>
                        <div class="text-sm text-gray-600">La défaillance nécessite des tests spécifiques ou des situations particulières pour être identifiée</div>
                    </div>
                    <div class="border-l-4 border-red-400 pl-3">
                        <div class="font-semibold">4 - Quasi impossible à détecter</div>
                        <div class="text-sm text-gray-600">La défaillance passe souvent inaperçue, même avec des vérifications</div>
                    </div>
                </div>
            </div>

            <!-- Gravité -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-4 text-orange-600">Échelle de Gravité</h3>
                <div class="space-y-3">
                    <div class="border-l-4 border-green-400 pl-3">
                        <div class="font-semibold">1 - Pas d'impact</div>
                        <div class="text-sm text-gray-600">La défaillance n'affecte ni le service, ni l'activité et n'ayant aucun impact sur le client</div>
                    </div>
                    <div class="border-l-4 border-yellow-400 pl-3">
                        <div class="font-semibold">2 - Impact léger</div>
                        <div class="text-sm text-gray-600">Désagréments mineurs, perturbation de l'activité mais sans impact sur le client</div>
                    </div>
                    <div class="border-l-4 border-orange-400 pl-3">
                        <div class="font-semibold">3 - Impact significatif</div>
                        <div class="text-sm text-gray-600">Non-conformité majeure, perturbation avec impact significatif sur le client</div>
                    </div>
                    <div class="border-l-4 border-red-400 pl-3">
                        <div class="font-semibold">4 - Impact critique</div>
                        <div class="text-sm text-gray-600">Dommages majeurs, arrêt d'activité, impact client majeur, risques de sécurité</div>
                    </div>
                </div>
            </div>

            <!-- Fréquence -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold mb-4 text-purple-600">Échelle de Fréquence</h3>
                <div class="space-y-3">
                    <div class="border-l-4 border-green-400 pl-3">
                        <div class="font-semibold">1 - Très rare</div>
                        <div class="text-sm text-gray-600">Probabilité très faible, événement exceptionnel (une fois par an)</div>
                    </div>
                    <div class="border-l-4 border-yellow-400 pl-3">
                        <div class="font-semibold">2 - Rare</div>
                        <div class="text-sm text-gray-600">Probabilité faible, peut arriver occasionnellement (2-4 fois par an)</div>
                    </div>
                    <div class="border-l-4 border-orange-400 pl-3">
                        <div class="font-semibold">3 - Peu fréquent</div>
                        <div class="text-sm text-gray-600">Probabilité modérée, peut arriver régulièrement</div>
                    </div>
                    <div class="border-l-4 border-red-400 pl-3">
                        <div class="font-semibold">4 - Fréquent</div>
                        <div class="text-sm text-gray-600">Probabilité élevée, arrive souvent</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Matrice de Criticité -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-xl font-semibold mb-4">Matrice de Criticité</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                        <h4 class="font-semibold text-green-800">Risques Tolérables</h4>
                    </div>
                    <div class="text-sm text-green-700 mb-2"><strong>IPR &lt; 8</strong></div>
                    <div class="text-sm text-gray-600">Risques acceptables - risques limités ayant un impact et une probabilité jugés faibles (zone verte)</div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
                        <h4 class="font-semibold text-yellow-800">Risques Mineurs</h4>
                    </div>
                    <div class="text-sm text-yellow-700 mb-2"><strong>8 ≤ IPR ≤ 18</strong></div>
                    <div class="text-sm text-gray-600">Risques sous contrôle - risques modérés à surveiller avec des mesures de contrôle efficaces (zone jaune)</div>
                </div>

                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-center mb-2">
                        <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
                        <h4 class="font-semibold text-red-800">Risques Critiques</h4>
                    </div>
                    <div class="text-sm text-red-700 mb-2"><strong>18 &lt; IPR ≤ 64</strong></div>
                    <div class="text-sm text-gray-600">Risques inacceptables - risques prioritaires impliquant un impact majeur sur l'activité (zone rouge)</div>
                </div>
            </div>
        </div>

        <!-- Matrice Visuelle -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-xl font-semibold mb-4">Matrice Visuelle des Risques</h3>
            <div class="overflow-x-auto">
                <table class="w-full border-collapse border border-gray-300">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="border border-gray-300 p-2 text-sm">Détection\Gravité×Fréquence</th>
                            <th class="border border-gray-300 p-2 text-sm">1×1=1</th>
                            <th class="border border-gray-300 p-2 text-sm">1×2=2</th>
                            <th class="border border-gray-300 p-2 text-sm">1×3=3</th>
                            <th class="border border-gray-300 p-2 text-sm">1×4=4</th>
                            <th class="border border-gray-300 p-2 text-sm">2×2=4</th>
                            <th class="border border-gray-300 p-2 text-sm">2×3=6</th>
                            <th class="border border-gray-300 p-2 text-sm">2×4=8</th>
                            <th class="border border-gray-300 p-2 text-sm">3×3=9</th>
                            <th class="border border-gray-300 p-2 text-sm">3×4=12</th>
                            <th class="border border-gray-300 p-2 text-sm">4×4=16</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="border border-gray-300 p-2 font-semibold">1</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">1</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">2</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">3</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">4</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">4</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">6</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">8</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">9</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">12</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">16</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 p-2 font-semibold">2</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">2</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">4</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">6</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">8</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">8</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">12</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">16</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">18</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">24</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">32</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 p-2 font-semibold">3</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">3</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">6</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">9</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">12</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">12</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">18</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">24</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">27</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">36</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">48</td>
                        </tr>
                        <tr>
                            <td class="border border-gray-300 p-2 font-semibold">4</td>
                            <td class="border border-gray-300 p-2 bg-green-100 text-center">4</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">8</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">12</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">16</td>
                            <td class="border border-gray-300 p-2 bg-yellow-100 text-center">16</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">24</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">32</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">36</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">48</td>
                            <td class="border border-gray-300 p-2 bg-red-100 text-center">64</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-sm text-gray-600">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-100 border mr-2"></div>
                        <span>Tolérable (IPR &lt; 8)</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-yellow-100 border mr-2"></div>
                        <span>Mineur (8 ≤ IPR ≤ 18)</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-red-100 border mr-2"></div>
                        <span>Critique (IPR &gt; 18)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script inline pour contourner le problème de cache -->
    <script>
        // Fonction Export définie directement dans le HTML
        function activerExportHTML() {
            console.log('🔄 Activation Export depuis HTML...');

            // Masquer toutes les sections
            const sections = ['dashboard', 'risques-list', 'new-risque', 'prediction-section', 'llama-section', 'methode-cotation-section'];
            sections.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('hidden');
                    console.log('Masqué:', id);
                }
            });

            // Afficher la section export
            const exportSection = document.getElementById('export-section');
            if (exportSection) {
                exportSection.classList.remove('hidden');
                console.log('✅ Section export affichée depuis HTML');

                // Charger les filtres d'export
                if (typeof loadExportFilters === 'function') {
                    loadExportFilters().catch(error => {
                        console.log('Erreur chargement filtres:', error);
                    });
                }
            } else {
                console.error('❌ Section export non trouvée');
            }
        }

        // Redéfinir activerExport pour utiliser la version HTML
        window.activerExport = activerExportHTML;

        // Fonction showAdvancedPrediction définie dans le HTML
        function showAdvancedPredictionHTML() {
            console.log('🤖 Affichage IA Avancée depuis HTML...');

            // Masquer toutes les sections
            const sections = ['dashboard', 'risques-list', 'new-risque', 'export-section', 'llama-section', 'methode-cotation-section'];
            sections.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('hidden');
                    console.log('Masqué:', id);
                }
            });

            // Afficher la section IA avancée
            const advancedSection = document.getElementById('advanced-prediction-section');
            if (advancedSection) {
                advancedSection.classList.remove('hidden');
                console.log('✅ Section IA Avancée affichée depuis HTML');

                // Vérifier le statut des modèles
                if (typeof checkAdvancedModelsStatus === 'function') {
                    checkAdvancedModelsStatus();
                }
            } else {
                console.error('❌ Section IA Avancée non trouvée');
            }
        }

        // Redéfinir la fonction globale
        window.showAdvancedPrediction = showAdvancedPredictionHTML;

        // Fonctions de gestion des modèles avancés dans le HTML
        window.checkAdvancedModelsStatus = async function() {
            try {
                console.log('🔍 Vérification statut modèles depuis HTML...');

                const response = await fetch('/api/predict/advanced/models/info');
                const status = await response.json();

                const statusDiv = document.getElementById('advanced-models-status');

                if (status.status === 'trained') {
                    statusDiv.innerHTML = `
                        <div class="flex items-center space-x-2 mb-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-green-700 font-medium">Modèles entraînés et prêts</span>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p>Modèles: ${status.models_available.join(', ')}</p>
                            <p>Ensemble: ${status.ensemble_available ? 'Oui' : 'Non'}</p>
                        </div>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span class="text-red-700 font-medium">Modèles non entraînés</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">Cliquez sur "Entraîner les modèles"</p>
                    `;
                }

            } catch (error) {
                console.error('Erreur vérification statut:', error);
                const statusDiv = document.getElementById('advanced-models-status');
                statusDiv.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <span class="text-yellow-700 font-medium">Erreur de connexion</span>
                    </div>
                `;
            }
        };

        window.trainAdvancedModels = async function() {
            try {
                console.log('🚀 Entraînement modèles depuis HTML...');

                const statusDiv = document.getElementById('advanced-models-status');
                statusDiv.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                        <span class="text-blue-700 font-medium">Entraînement en cours...</span>
                    </div>
                `;

                const response = await fetch('/api/predict/advanced/train', { method: 'POST' });
                const result = await response.json();

                if (result.status === 'training_started') {
                    alert('Entraînement démarré en arrière-plan (2-5 minutes)');
                }

            } catch (error) {
                console.error('Erreur entraînement:', error);
                alert('Erreur lors du démarrage de l\'entraînement');
            }
        };

        window.retrainAdvancedModels = async function() {
            try {
                const response = await fetch('/api/predict/advanced/retrain', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ force: true })
                });
                const result = await response.json();

                if (result.status === 'retrain_started') {
                    alert('Réentraînement démarré en arrière-plan');
                }

            } catch (error) {
                console.error('Erreur réentraînement:', error);
                alert('Erreur lors du réentraînement');
            }
        };

        // Ajouter l'event listener dès que le DOM est prêt
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const exportBtn = document.getElementById('export-btn');
                if (exportBtn) {
                    // Supprimer tous les event listeners existants
                    exportBtn.replaceWith(exportBtn.cloneNode(true));
                    const newExportBtn = document.getElementById('export-btn');

                    newExportBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🔄 Clic Export détecté depuis HTML');
                        activerExportHTML();
                    });
                    console.log('✅ Event listener Export ajouté depuis HTML');
                } else {
                    console.error('❌ Bouton export non trouvé depuis HTML');
                }
            }, 2000); // Attendre 2 secondes pour s'assurer que tout est chargé
        });
    </script>
</body>
</html>
