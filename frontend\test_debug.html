<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Debug - Création et Modification</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        textarea, input, select { margin: 5px; padding: 5px; width: 300px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>🔍 Test Debug - Création et Modification de Risques</h1>
    
    <div class="test-section">
        <h2>Test 1: Vérification de l'API</h2>
        <button onclick="testAPI()">Tester l'API</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Création de Risque (comme le frontend)</h2>
        <form id="test-form">
            <div>
                <label>Processus:</label><br>
                <input type="text" id="processus" value="Test Debug" required>
            </div>
            <div>
                <label>Description:</label><br>
                <textarea id="description" required>Test de création via formulaire debug</textarea>
            </div>
            <div>
                <label>Moyen de Détection:</label><br>
                <textarea id="moyen_detection">Test moyen de détection</textarea>
            </div>
            <div>
                <label>Moyen de Maîtrise:</label><br>
                <textarea id="moyen_maitrise">Test moyen de maîtrise</textarea>
            </div>
            <div>
                <label>Note Détection:</label><br>
                <select id="note_detection" required>
                    <option value="1">1</option>
                    <option value="2" selected>2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                </select>
            </div>
            <div>
                <label>Note Gravité:</label><br>
                <select id="note_gravite" required>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3" selected>3</option>
                    <option value="4">4</option>
                </select>
            </div>
            <div>
                <label>Note Fréquence:</label><br>
                <select id="note_frequence" required>
                    <option value="1">1</option>
                    <option value="2" selected>2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                </select>
            </div>
            <div>
                <label>Actions:</label><br>
                <textarea id="actions">Test actions</textarea>
            </div>
            <div>
                <label>Responsables:</label><br>
                <input type="text" id="responsables" value="Test responsable">
            </div>
            <button type="submit">Créer Risque</button>
        </form>
        <div id="creation-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Modification de Risque</h2>
        <div>
            <label>ID du risque à modifier:</label><br>
            <input type="number" id="risque-id" placeholder="Entrez l'ID d'un risque existant">
            <button onclick="loadRisqueForEdit()">Charger pour Modification</button>
        </div>
        <div id="modification-result" class="result"></div>
    </div>

    <script>
        let currentEditingRisque = null;

        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="info">Test de l\'API en cours...</div>';
            
            try {
                // Test 1: Health check
                const healthResponse = await axios.get('/api/health');
                console.log('Health check:', healthResponse.data);
                
                // Test 2: Get risques
                const risquesResponse = await axios.get('/api/risques?limit=3');
                console.log('Risques:', risquesResponse.data);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ API fonctionne</div>
                    <div>Health: ${JSON.stringify(healthResponse.data)}</div>
                    <div>Nombre de risques: ${risquesResponse.data.length}</div>
                `;
                
            } catch (error) {
                console.error('Erreur API:', error);
                resultDiv.innerHTML = `<div class="error">❌ Erreur API: ${error.message}</div>`;
            }
        }

        // Formulaire de création
        document.getElementById('test-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('creation-result');
            resultDiv.innerHTML = '<div class="info">Création en cours...</div>';
            
            try {
                // Construire les données exactement comme le frontend principal
                const formData = {
                    processus: document.getElementById('processus').value,
                    source: document.getElementById('source')?.value || null,
                    description: document.getElementById('description').value,
                    causes: document.getElementById('causes')?.value || null,
                    effets: document.getElementById('effets')?.value || null,
                    moyens_detection: document.getElementById('moyen_detection').value || null,
                    moyens_maitrise: document.getElementById('moyen_maitrise').value || null,
                    note_detection: parseInt(document.getElementById('note_detection').value),
                    note_gravite: parseInt(document.getElementById('note_gravite').value),
                    note_frequence: parseInt(document.getElementById('note_frequence').value),
                    actions_risques: document.getElementById('actions').value || null,
                    responsables: document.getElementById('responsables').value || null
                };
                
                console.log('Données envoyées:', formData);
                
                let response;
                if (currentEditingRisque) {
                    // Mode modification
                    response = await axios.put(`/api/risques/${currentEditingRisque}`, formData);
                    resultDiv.innerHTML = `<div class="success">✅ Risque modifié avec succès! ID: ${currentEditingRisque}</div>`;
                    currentEditingRisque = null;
                } else {
                    // Mode création
                    response = await axios.post('/api/risques', formData);
                    resultDiv.innerHTML = `<div class="success">✅ Risque créé avec succès! ID: ${response.data.id}</div>`;
                }
                
                console.log('Réponse:', response.data);
                resultDiv.innerHTML += `<pre>${JSON.stringify(response.data, null, 2)}</pre>`;
                
                // Réinitialiser le formulaire
                document.getElementById('test-form').reset();
                
            } catch (error) {
                console.error('Erreur création/modification:', error);
                resultDiv.innerHTML = `
                    <div class="error">❌ Erreur: ${error.message}</div>
                    <div>Status: ${error.response?.status}</div>
                    <div>Détails: ${JSON.stringify(error.response?.data, null, 2)}</div>
                `;
            }
        });

        async function loadRisqueForEdit() {
            const risqueId = document.getElementById('risque-id').value;
            const resultDiv = document.getElementById('modification-result');
            
            if (!risqueId) {
                resultDiv.innerHTML = '<div class="error">❌ Veuillez entrer un ID de risque</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Chargement du risque...</div>';
            
            try {
                const response = await axios.get(`/api/risques/${risqueId}`);
                const risque = response.data;
                
                console.log('Risque chargé:', risque);
                
                // Remplir le formulaire
                document.getElementById('processus').value = risque.processus;
                document.getElementById('description').value = risque.description;
                document.getElementById('moyen_detection').value = risque.moyens_detection || '';
                document.getElementById('moyen_maitrise').value = risque.moyens_maitrise || '';
                document.getElementById('note_detection').value = risque.note_detection;
                document.getElementById('note_gravite').value = risque.note_gravite;
                document.getElementById('note_frequence').value = risque.note_frequence;
                document.getElementById('actions').value = risque.actions_risques || '';
                document.getElementById('responsables').value = risque.responsables || '';
                
                currentEditingRisque = risqueId;
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Risque chargé pour modification</div>
                    <div>Processus: ${risque.processus}</div>
                    <div>Description: ${risque.description}</div>
                    <div>Moyens détection: ${risque.moyens_detection || 'N/A'}</div>
                    <div>Moyens maîtrise: ${risque.moyens_maitrise || 'N/A'}</div>
                    <div>Actions: ${risque.actions_risques || 'N/A'}</div>
                `;
                
            } catch (error) {
                console.error('Erreur chargement:', error);
                resultDiv.innerHTML = `
                    <div class="error">❌ Erreur chargement: ${error.message}</div>
                    <div>Status: ${error.response?.status}</div>
                    <div>Détails: ${JSON.stringify(error.response?.data, null, 2)}</div>
                `;
            }
        }
    </script>
</body>
</html>
