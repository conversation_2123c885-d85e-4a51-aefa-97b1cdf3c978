<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test - Création/Modification Risques</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🧪 Test - Création/Modification de Risques</h1>
    
    <div id="message"></div>
    
    <form id="test-form">
        <div class="form-group">
            <label>Processus *</label>
            <select id="processus" required>
                <option value="">Sélectionner</option>
                <option value="Production">Production</option>
                <option value="Commercial">Commercial</option>
                <option value="IT">IT</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Description *</label>
            <textarea id="description" required placeholder="Description du risque"></textarea>
        </div>
        
        <div class="form-group">
            <label>Note Détection (1-4) *</label>
            <select id="note_detection" required>
                <option value="">Sélectionner</option>
                <option value="1">1 - Très facile</option>
                <option value="2">2 - Facile</option>
                <option value="3">3 - Difficile</option>
                <option value="4">4 - Quasi impossible</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Note Gravité (1-4) *</label>
            <select id="note_gravite" required>
                <option value="">Sélectionner</option>
                <option value="1">1 - Pas d'impact</option>
                <option value="2">2 - Impact léger</option>
                <option value="3">3 - Impact significatif</option>
                <option value="4">4 - Impact critique</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>Note Fréquence (1-4) *</label>
            <select id="note_frequence" required>
                <option value="">Sélectionner</option>
                <option value="1">1 - Exceptionnel</option>
                <option value="2">2 - Rare</option>
                <option value="3">3 - Peu fréquent</option>
                <option value="4">4 - Fréquent</option>
            </select>
        </div>
        
        <button type="submit">Créer le Risque</button>
        <button type="button" onclick="loadRisques()">Charger les Risques</button>
        <button type="button" onclick="clearForm()">Vider le Formulaire</button>
    </form>
    
    <h2>📋 Liste des Risques</h2>
    <div id="risques-list"></div>
    
    <script>
        const API_BASE_URL = 'http://127.0.0.1:8000';
        axios.defaults.baseURL = API_BASE_URL;
        
        let currentEditingRisque = null;
        
        function showMessage(message, isError = false) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
            setTimeout(() => messageDiv.innerHTML = '', 3000);
        }
        
        document.getElementById('test-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('Formulaire soumis!');
            
            const formData = {
                processus: document.getElementById('processus').value,
                description: document.getElementById('description').value,
                note_detection: parseInt(document.getElementById('note_detection').value),
                note_gravite: parseInt(document.getElementById('note_gravite').value),
                note_frequence: parseInt(document.getElementById('note_frequence').value),
                source: null,
                causes: null,
                effets: null,
                moyens_detection: null,
                moyens_maitrise: null,
                actions_risques: null,
                responsables: null
            };
            
            console.log('Données du formulaire:', formData);
            
            try {
                let response;
                
                if (currentEditingRisque) {
                    console.log('Mode modification, ID:', currentEditingRisque);
                    response = await axios.put(`/api/risques/${currentEditingRisque}`, formData);
                    showMessage('✅ Risque modifié avec succès!');
                    currentEditingRisque = null;
                    document.querySelector('button[type="submit"]').textContent = 'Créer le Risque';
                } else {
                    console.log('Mode création');
                    response = await axios.post('/api/risques', formData);
                    showMessage('✅ Risque créé avec succès!');
                }
                
                console.log('Réponse API:', response.data);
                clearForm();
                loadRisques();
                
            } catch (error) {
                console.error('Erreur:', error);
                showMessage('❌ Erreur: ' + (error.response?.data?.detail || error.message), true);
            }
        });
        
        async function loadRisques() {
            try {
                const response = await axios.get('/api/risques');
                const risques = response.data;
                
                const listDiv = document.getElementById('risques-list');
                listDiv.innerHTML = risques.map(risque => `
                    <div style="border: 1px solid #ccc; margin: 10px 0; padding: 10px;">
                        <strong>ID: ${risque.id}</strong> - ${risque.processus}<br>
                        <em>${risque.description}</em><br>
                        IPR: ${risque.ipr} | Criticité: ${risque.criticite}<br>
                        <button onclick="editRisque(${risque.id})">Modifier</button>
                        <button onclick="deleteRisque(${risque.id})" style="background: red;">Supprimer</button>
                    </div>
                `).join('');
                
                showMessage(`📊 ${risques.length} risques chargés`);
                
            } catch (error) {
                console.error('Erreur chargement:', error);
                showMessage('❌ Erreur lors du chargement des risques', true);
            }
        }
        
        async function editRisque(id) {
            try {
                const response = await axios.get(`/api/risques/${id}`);
                const risque = response.data;
                
                // Remplir le formulaire
                document.getElementById('processus').value = risque.processus;
                document.getElementById('description').value = risque.description;
                document.getElementById('note_detection').value = risque.note_detection;
                document.getElementById('note_gravite').value = risque.note_gravite;
                document.getElementById('note_frequence').value = risque.note_frequence;
                
                currentEditingRisque = id;
                document.querySelector('button[type="submit"]').textContent = 'Modifier le Risque';
                
                showMessage(`📝 Risque #${id} chargé pour modification`);
                
            } catch (error) {
                console.error('Erreur:', error);
                showMessage('❌ Erreur lors du chargement du risque', true);
            }
        }
        
        async function deleteRisque(id) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer ce risque ?')) return;
            
            try {
                await axios.delete(`/api/risques/${id}`);
                showMessage('🗑️ Risque supprimé avec succès!');
                loadRisques();
            } catch (error) {
                console.error('Erreur:', error);
                showMessage('❌ Erreur lors de la suppression', true);
            }
        }
        
        function clearForm() {
            document.getElementById('test-form').reset();
            currentEditingRisque = null;
            document.querySelector('button[type="submit"]').textContent = 'Créer le Risque';
        }
        
        // Charger les risques au démarrage
        window.addEventListener('load', loadRisques);
    </script>
</body>
</html>
