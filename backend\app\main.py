from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os

from .models.database import engine, Base
from .routers import risques, opportunites, dashboard, export
from .routers import prediction_simple as prediction

# Créer les tables de la base de données
Base.metadata.create_all(bind=engine)

# Créer l'application FastAPI
app = FastAPI(
    title="API Gestion des Risques",
    description="API pour la gestion et l'analyse des risques et opportunités",
    version="1.0.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Inclure les routers
app.include_router(risques.router)
app.include_router(opportunites.router)
app.include_router(dashboard.router)
app.include_router(export.router)
app.include_router(prediction.router)

# Router prédiction avancée
try:
    from .routers import advanced_prediction
    app.include_router(advanced_prediction.router)
    print("✅ Router prédiction avancée ajouté")
except ImportError as e:
    print(f"⚠️  Router prédiction avancée non disponible: {e}")

# Import et ajout du router Llama
try:
    from .routers import llama_prediction
    app.include_router(llama_prediction.router)
except ImportError:
    print("⚠️  Router Llama non disponible - httpx requis pour cette fonctionnalité")

# Router méthode de cotation
from .routers import methode_cotation
app.include_router(methode_cotation.router)

# Servir les fichiers statiques (pour les exports)
if not os.path.exists("static"):
    os.makedirs("static")
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
def read_root():
    return {
        "message": "API Gestion des Risques",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
