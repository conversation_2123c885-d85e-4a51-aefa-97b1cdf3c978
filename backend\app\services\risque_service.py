from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..models.risque import Risque, Opportunite, ProcessusStats
from ..schemas import RisqueCreate, RisqueUpdate, OpportuniteCreate, OpportuniteUpdate
from ..schemas import DashboardStats, ProcessusStatsResponse, ChartData, DashboardCharts

class RisqueService:
    
    @staticmethod
    def create_risque(db: Session, risque_data: RisqueCreate) -> Risque:
        """Crée un nouveau risque"""
        risque = Risque(**risque_data.dict())
        risque.update_calculated_fields()  # Calcule IPR et niveau de criticité
        
        db.add(risque)
        db.commit()
        db.refresh(risque)
        
        # Mettre à jour les statistiques du processus
        RisqueService._update_processus_stats(db, risque.processus)
        
        return risque
    
    @staticmethod
    def get_risque(db: Session, risque_id: int) -> Optional[Risque]:
        """Récupère un risque par son ID"""
        return db.query(Risque).filter(Risque.id == risque_id).first()
    
    @staticmethod
    def get_risques(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        processus: Optional[str] = None,
        niveau_criticite: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Risque]:
        """Récupère une liste de risques avec filtres optionnels"""
        query = db.query(Risque)
        
        if processus:
            query = query.filter(Risque.processus == processus)
        
        if niveau_criticite:
            query = query.filter(Risque.niveau_criticite == niveau_criticite)
        
        if search:
            search_filter = or_(
                Risque.description.contains(search),
                Risque.causes.contains(search),
                Risque.effets.contains(search)
            )
            query = query.filter(search_filter)
        
        return query.offset(skip).limit(limit).all()
    
    @staticmethod
    def update_risque(db: Session, risque_id: int, risque_data: RisqueUpdate) -> Optional[Risque]:
        """Met à jour un risque existant"""
        risque = db.query(Risque).filter(Risque.id == risque_id).first()
        if not risque:
            return None
        
        # Mettre à jour les champs fournis
        update_data = risque_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(risque, field, value)
        
        # Recalculer les champs automatiques si nécessaire
        if any(field in update_data for field in ['note_detection', 'note_gravite', 'note_frequence']):
            risque.update_calculated_fields()
        
        db.commit()
        db.refresh(risque)
        
        # Mettre à jour les statistiques du processus
        RisqueService._update_processus_stats(db, risque.processus)
        
        return risque
    
    @staticmethod
    def delete_risque(db: Session, risque_id: int) -> bool:
        """Supprime un risque"""
        risque = db.query(Risque).filter(Risque.id == risque_id).first()
        if not risque:
            return False
        
        processus = risque.processus
        db.delete(risque)
        db.commit()
        
        # Mettre à jour les statistiques du processus
        RisqueService._update_processus_stats(db, processus)
        
        return True
    
    @staticmethod
    def get_dashboard_stats(db: Session) -> DashboardStats:
        """Récupère les statistiques pour le dashboard"""
        
        # Statistiques générales
        total_risques = db.query(Risque).count()
        total_opportunites = db.query(Opportunite).count()
        
        # Distribution par niveau de criticité
        risques_critiques = db.query(Risque).filter(
            Risque.niveau_criticite == "Critique (IPR > 18)"
        ).count()
        
        risques_mineurs = db.query(Risque).filter(
            Risque.niveau_criticite == "Mineur (8 ≤ IPR ≤ 18)"
        ).count()
        
        risques_tolerables = db.query(Risque).filter(
            Risque.niveau_criticite == "Tolérable (IPR < 8)"
        ).count()
        
        # IPR moyen
        ipr_moyen = db.query(func.avg(Risque.ipr)).scalar() or 0.0
        
        # Statistiques par processus
        processus_stats = RisqueService.get_processus_stats(db)
        
        return DashboardStats(
            total_risques=total_risques,
            total_opportunites=total_opportunites,
            risques_critiques=risques_critiques,
            risques_mineurs=risques_mineurs,
            risques_tolerables=risques_tolerables,
            ipr_moyen=round(ipr_moyen, 2),
            processus_stats=processus_stats
        )
    
    @staticmethod
    def get_dashboard_charts(db: Session) -> DashboardCharts:
        """Récupère les données pour les graphiques du dashboard"""
        
        # Distribution par criticité
        criticite_data = db.query(
            Risque.niveau_criticite,
            func.count(Risque.id)
        ).group_by(Risque.niveau_criticite).all()
        
        distribution_criticite = ChartData(
            labels=[item[0] for item in criticite_data],
            data=[item[1] for item in criticite_data],
            colors=["#10B981", "#F59E0B", "#EF4444"]  # Vert, Orange, Rouge
        )
        
        # Distribution par processus
        processus_data = db.query(
            Risque.processus,
            func.count(Risque.id)
        ).group_by(Risque.processus).all()
        
        distribution_processus = ChartData(
            labels=[item[0] for item in processus_data],
            data=[item[1] for item in processus_data]
        )
        
        # Évolution temporelle (derniers 12 mois)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)

        # Pour SQLite, utiliser strftime au lieu de date_trunc
        evolution_data = db.query(
            func.strftime('%Y-%m', Risque.created_at).label('month'),
            func.count(Risque.id)
        ).filter(
            Risque.created_at >= start_date
        ).group_by('month').order_by('month').all()

        evolution_temporelle = {
            "labels": [item[0] for item in evolution_data],
            "data": [item[1] for item in evolution_data]
        }

        # Criticité par Processus (pour bar chart)
        criticite_processus_data = db.query(
            Risque.processus,
            Risque.niveau_criticite,
            func.count(Risque.id)
        ).group_by(Risque.processus, Risque.niveau_criticite).all()

        # Organiser les données par processus
        processus_criticite_map = {}
        for processus, criticite, count in criticite_processus_data:
            if processus not in processus_criticite_map:
                processus_criticite_map[processus] = {
                    'Tolérable (IPR < 8)': 0,
                    'Mineur (8 ≤ IPR ≤ 18)': 0,
                    'Critique (IPR > 18)': 0
                }
            processus_criticite_map[processus][criticite] = count

        # Créer les datasets pour le graphique en barres empilées
        processus_labels = list(processus_criticite_map.keys())
        tolerables_data = [processus_criticite_map[p]['Tolérable (IPR < 8)'] for p in processus_labels]
        mineurs_data = [processus_criticite_map[p]['Mineur (8 ≤ IPR ≤ 18)'] for p in processus_labels]
        critiques_data = [processus_criticite_map[p]['Critique (IPR > 18)'] for p in processus_labels]

        criticite_par_processus = ChartData(
            labels=processus_labels,
            data=[tolerables_data, mineurs_data, critiques_data],
            colors=["#10B981", "#F59E0B", "#EF4444"]  # Vert, Orange, Rouge
        )

        # IPR Moyen par Processus (pour line chart)
        ipr_processus_data = db.query(
            Risque.processus,
            func.avg(Risque.ipr)
        ).group_by(Risque.processus).all()

        ipr_moyen_par_processus = ChartData(
            labels=[item[0] for item in ipr_processus_data],
            data=[round(float(item[1]), 2) for item in ipr_processus_data],
            colors=["#8B5CF6"]  # Violet
        )

        return DashboardCharts(
            distribution_criticite=distribution_criticite,
            distribution_processus=distribution_processus,
            evolution_temporelle=evolution_temporelle,
            criticite_par_processus=criticite_par_processus,
            ipr_moyen_par_processus=ipr_moyen_par_processus
        )
    
    @staticmethod
    def get_processus_stats(db: Session) -> List[ProcessusStatsResponse]:
        """Récupère les statistiques par processus"""
        
        # Requête pour obtenir les statistiques par processus
        from sqlalchemy import case
        stats = db.query(
            Risque.processus,
            func.count(Risque.id).label('total'),
            func.sum(case((Risque.niveau_criticite == "Tolérable (IPR < 8)", 1), else_=0)).label('tolerables'),
            func.sum(case((Risque.niveau_criticite == "Mineur (8 ≤ IPR ≤ 18)", 1), else_=0)).label('mineurs'),
            func.sum(case((Risque.niveau_criticite == "Critique (IPR > 18)", 1), else_=0)).label('critiques'),
            func.avg(Risque.ipr).label('ipr_moyen')
        ).group_by(Risque.processus).all()
        
        return [
            ProcessusStatsResponse(
                processus=stat.processus,
                total_risques=stat.total,
                risques_tolerables=stat.tolerables or 0,
                risques_mineurs=stat.mineurs or 0,
                risques_critiques=stat.critiques or 0,
                ipr_moyen=round(stat.ipr_moyen or 0, 2)
            )
            for stat in stats
        ]
    
    @staticmethod
    def _update_processus_stats(db: Session, processus: str):
        """Met à jour les statistiques d'un processus spécifique"""
        
        # Calculer les nouvelles statistiques
        from sqlalchemy import case
        stats = db.query(
            func.count(Risque.id).label('total'),
            func.sum(case((Risque.niveau_criticite == "Tolérable (IPR < 8)", 1), else_=0)).label('tolerables'),
            func.sum(case((Risque.niveau_criticite == "Mineur (8 ≤ IPR ≤ 18)", 1), else_=0)).label('mineurs'),
            func.sum(case((Risque.niveau_criticite == "Critique (IPR > 18)", 1), else_=0)).label('critiques'),
            func.avg(Risque.ipr).label('ipr_moyen')
        ).filter(Risque.processus == processus).first()
        
        # Mettre à jour ou créer l'enregistrement des statistiques
        processus_stats = db.query(ProcessusStats).filter(
            ProcessusStats.processus == processus
        ).first()
        
        if not processus_stats:
            processus_stats = ProcessusStats(processus=processus)
            db.add(processus_stats)
        
        processus_stats.total_risques = stats.total or 0
        processus_stats.risques_tolerables = stats.tolerables or 0
        processus_stats.risques_mineurs = stats.mineurs or 0
        processus_stats.risques_critiques = stats.critiques or 0
        processus_stats.ipr_moyen = round(stats.ipr_moyen or 0, 2)
        
        db.commit()

class OpportuniteService:
    
    @staticmethod
    def create_opportunite(db: Session, opportunite_data: OpportuniteCreate) -> Opportunite:
        """Crée une nouvelle opportunité"""
        opportunite = Opportunite(**opportunite_data.dict())
        
        db.add(opportunite)
        db.commit()
        db.refresh(opportunite)
        
        return opportunite
    
    @staticmethod
    def get_opportunite(db: Session, opportunite_id: int) -> Optional[Opportunite]:
        """Récupère une opportunité par son ID"""
        return db.query(Opportunite).filter(Opportunite.id == opportunite_id).first()
    
    @staticmethod
    def get_opportunites(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        processus: Optional[str] = None
    ) -> List[Opportunite]:
        """Récupère une liste d'opportunités avec filtres optionnels"""
        query = db.query(Opportunite)
        
        if processus:
            query = query.filter(Opportunite.processus == processus)
        
        return query.offset(skip).limit(limit).all()
    
    @staticmethod
    def update_opportunite(db: Session, opportunite_id: int, opportunite_data: OpportuniteUpdate) -> Optional[Opportunite]:
        """Met à jour une opportunité existante"""
        opportunite = db.query(Opportunite).filter(Opportunite.id == opportunite_id).first()
        if not opportunite:
            return None
        
        # Mettre à jour les champs fournis
        update_data = opportunite_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(opportunite, field, value)
        
        db.commit()
        db.refresh(opportunite)
        
        return opportunite
    
    @staticmethod
    def delete_opportunite(db: Session, opportunite_id: int) -> bool:
        """Supprime une opportunité"""
        opportunite = db.query(Opportunite).filter(Opportunite.id == opportunite_id).first()
        if not opportunite:
            return False
        
        db.delete(opportunite)
        db.commit()
        
        return True
