from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from ..models.database import get_db
from ..schemas import DashboardStats, DashboardCharts, ProcessusStatsResponse
from ..services.risque_service import RisqueService
from typing import List

router = APIRouter(prefix="/api/dashboard", tags=["dashboard"])

@router.get("/stats", response_model=DashboardStats)
def get_dashboard_stats(db: Session = Depends(get_db)):
    """Récupère les statistiques principales pour le dashboard"""
    return RisqueService.get_dashboard_stats(db)

@router.get("/charts", response_model=DashboardCharts)
def get_dashboard_charts(db: Session = Depends(get_db)):
    """Récupère les données pour les graphiques du dashboard"""
    return RisqueService.get_dashboard_charts(db)

@router.get("/processus-stats", response_model=List[ProcessusStatsResponse])
def get_processus_stats(db: Session = Depends(get_db)):
    """Récupère les statistiques détaillées par processus"""
    return RisqueService.get_processus_stats(db)
