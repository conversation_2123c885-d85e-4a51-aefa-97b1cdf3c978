# 📖 Guide d'Utilisation - Application de Gestion des Risques

## 🚀 Démarrage Rapide

### Accès à l'Application
1. **Interface Web** : Ouvrez votre navigateur et allez sur `http://localhost:3000`
2. **Documentation API** : Consultez `http://127.0.0.1:8000/docs` pour l'API

### Navigation Principale
L'application dispose de 5 sections principales accessibles via la barre de navigation :
- **Dashboard** : Vue d'ensemble et statistiques
- **Risques** : Liste et gestion des risques
- **Nouveau Risque** : Création de nouveaux risques
- **Prédiction IA** : Analyse intelligente des risques
- **Export** : Génération de rapports

---

## 📊 Dashboard - Vue d'Ensemble

### Statistiques Principales
Le dashboard affiche en temps réel :
- **Total des risques** enregistrés
- **Risques critiques** nécessitant une attention immédiate
- **Risques mineurs** à surveiller
- **IPR moyen** de l'ensemble des risques

### Graphiques Interactifs
- **Distribution par Criticité** : Répartition des risques par niveau (Tolérable/Mineur/Critique)
- **Distribution par Processus** : Nombre de risques par processus métier

---

## 🔧 Gestion des Risques

### Consulter les Risques
1. Cliquez sur **"Risques"** dans la navigation
2. Utilisez les **filtres** pour affiner votre recherche :
   - **Processus** : Filtrer par type de processus
   - **Criticité** : Filtrer par niveau de risque
   - **Recherche textuelle** : Rechercher dans les descriptions

### Créer un Nouveau Risque
1. Cliquez sur **"Nouveau Risque"**
2. Remplissez le formulaire :

#### Informations Obligatoires
- **Processus** : Sélectionnez le processus concerné
- **Description du Risque** : Décrivez clairement le risque
- **Note Détection** (1-4) : Facilité de détection du risque
- **Note Gravité** (1-4) : Impact potentiel du risque
- **Note Fréquence** (1-4) : Probabilité d'occurrence

#### Informations Optionnelles
- **Source** : Origine du risque
- **Causes** : Causes identifiées
- **Effets** : Conséquences potentielles
- **Actions** : Mesures de mitigation
- **Responsables** : Personnes en charge

#### Calculs Automatiques
- **IPR** : Calculé automatiquement (Détection × Gravité × Fréquence)
- **Niveau de Criticité** : Déterminé selon l'IPR
  - Tolérable : IPR < 8
  - Mineur : 8 ≤ IPR ≤ 18
  - Critique : IPR > 18

### 🤖 Prédiction IA Intégrée
Dans le formulaire de création, utilisez le bouton **"🔮 Prédire le niveau"** pour :
- Obtenir une estimation IA du niveau de criticité
- Voir le pourcentage de confiance de la prédiction
- Valider votre évaluation manuelle

---

## 🤖 Prédiction IA - Analyse Intelligente

### Utilisation de l'IA
1. Accédez à la section **"Prédiction IA"**
2. Remplissez le formulaire d'analyse :
   - **Processus** : Type de processus (optionnel)
   - **Description** : Description détaillée du risque
   - **Causes** : Causes potentielles (optionnel)
   - **Effets** : Effets possibles (optionnel)

3. Cliquez sur **"🔮 Analyser avec l'IA"**

### Résultats de l'Analyse
L'IA vous fournit :
- **Niveau de Criticité Prédit** : Tolérable, Mineur ou Critique
- **Confiance** : Pourcentage de certitude de la prédiction
- **Facteurs d'Analyse** :
  - Longueur du texte analysé
  - Nombre de mots-clés critiques détectés
  - Présence de causes et effets détaillés
  - Type de processus

### Recommandations Automatiques
Basées sur l'analyse, l'IA propose :
- **Actions recommandées** selon le niveau de criticité
- **Conseils d'amélioration** pour la description du risque
- **Suggestions** pour optimiser l'analyse

### Exemples de Test
Utilisez les **exemples prédéfinis** pour tester l'IA :
- **Risque Critique** : Panne serveur production
- **Risque Mineur** : Erreur facturation
- **Risque Tolérable** : Formation personnel

---

## 📄 Export et Rapports

### Génération de Rapports
1. Accédez à la section **"Export"**
2. Configurez votre rapport :

#### Format d'Export
- **PDF** : Rapport professionnel avec graphiques
- **Word (DOCX)** : Document éditable avec tableaux

#### Filtres Disponibles
- **Processus** : Sélectionnez un ou plusieurs processus
- **Criticité** : Filtrez par niveau de risque
- **Période** : Définissez une plage de dates

#### Contenu du Rapport
- **Résumé exécutif** avec statistiques clés
- **Distribution par processus** et criticité
- **Détail complet** de chaque risque sélectionné
- **Graphiques** et tableaux de synthèse

### Téléchargement
1. Cliquez sur **"Générer le Rapport"**
2. Attendez la génération (quelques secondes)
3. Cliquez sur **"Télécharger le Rapport"** quand il est prêt

---

## 💡 Conseils d'Utilisation

### Pour une Meilleure Prédiction IA
- **Soyez précis** : Plus la description est détaillée, plus la prédiction est fiable
- **Incluez les causes** : Aidez l'IA à comprendre le contexte
- **Décrivez les effets** : Permettez une meilleure évaluation de l'impact
- **Utilisez un vocabulaire technique** : L'IA reconnaît les termes métier

### Gestion Efficace des Risques
- **Mettez à jour régulièrement** : Révisez vos risques périodiquement
- **Utilisez les filtres** : Concentrez-vous sur les risques critiques
- **Exportez régulièrement** : Créez des rapports pour le suivi
- **Validez avec l'IA** : Utilisez la prédiction pour confirmer vos évaluations

### Bonnes Pratiques
- **Processus standardisés** : Utilisez les mêmes processus pour la cohérence
- **Descriptions claires** : Évitez les abréviations et soyez explicite
- **Actions concrètes** : Définissez des mesures de mitigation précises
- **Responsabilités claires** : Assignez des responsables identifiés

---

## 🆘 Support et Dépannage

### Problèmes Courants

#### L'interface ne se charge pas
- Vérifiez que le serveur frontend est démarré sur le port 3000
- Actualisez la page (F5)

#### Les données ne s'affichent pas
- Vérifiez que l'API est démarrée sur le port 8000
- Consultez la console du navigateur (F12) pour les erreurs

#### La prédiction IA ne fonctionne pas
- Assurez-vous d'avoir saisi une description
- Vérifiez la connexion à l'API

#### L'export ne se télécharge pas
- Vérifiez votre navigateur pour les téléchargements bloqués
- Essayez avec un autre format (PDF/Word)

### Informations Techniques
- **Frontend** : http://localhost:3000
- **API** : http://127.0.0.1:8000
- **Documentation API** : http://127.0.0.1:8000/docs
- **Fichiers d'export** : Dossier `static/` du serveur

---

## 📈 Métriques et Performance

### Données Actuelles
- **98 risques** importés et analysés
- **8 processus** métier couverts
- **Précision IA** : Basée sur analyse textuelle avancée
- **Temps de réponse** : < 2 secondes pour les prédictions

### Indicateurs de Qualité
- **Cohérence IPR** : 100% des calculs corrects
- **Disponibilité** : Interface et API opérationnelles 24/7
- **Performance** : Chargement instantané des données

---

*Pour toute question ou suggestion d'amélioration, consultez la documentation technique ou contactez l'équipe de développement.*
