"""
Serveur HTTP simple pour servir l'interface frontend
"""
import http.server
import socketserver
import os
import webbrowser
import threading
import time

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

def start_frontend_server():
    """Démarre le serveur frontend"""
    PORT = 3000
    
    # Changer vers le répertoire frontend
    os.chdir('frontend')
    
    with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
        print(f"🌐 Serveur frontend démarré sur http://localhost:{PORT}")
        print(f"📁 Répertoire: {os.getcwd()}")
        
        # Ouvrir le navigateur après un délai
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{PORT}')

        threading.Thread(target=open_browser, daemon=True).start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Serveur frontend arrêté")

if __name__ == "__main__":
    start_frontend_server()
