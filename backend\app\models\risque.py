from sqlalchemy import Column, Integer, String, Text, DateTime, Float, JSON
from sqlalchemy.sql import func
from .database import Base

class Risque(Base):
    __tablename__ = "risques"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Identification du risque
    processus = Column(String(100), nullable=False, index=True)
    source = Column(String(100))
    enjeux = Column(Text)
    description = Column(Text, nullable=False)
    causes = Column(Text)
    effets = Column(Text)
    
    # Évaluation du risque
    moyens_detection = Column(Text)
    note_detection = Column(Integer, nullable=False)  # 1-4
    note_gravite = Column(Integer, nullable=False)    # 1-4
    note_frequence = Column(Integer, nullable=False)  # 1-4
    ipr = Column(Integer, nullable=False)             # Calculé automatiquement
    niveau_criticite = Column(String(50), nullable=False)  # Calculé automatiquement
    
    # Traitement du risque
    moyens_maitrise = Column(Text)
    actions = Column(Text)
    responsables = Column(String(200))
    delai = Column(DateTime)
    
    # Mesure d'efficacité
    methode_evaluation = Column(Text)
    date_mesure = Column(DateTime)
    evaluation_efficacite = Column(Text)
    
    # Métadonnées
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def calculate_ipr(self):
        """Calcule l'IPR basé sur les notes de détection, gravité et fréquence"""
        return self.note_detection * self.note_gravite * self.note_frequence
    
    def get_niveau_criticite(self):
        """Détermine le niveau de criticité basé sur l'IPR"""
        if self.ipr < 8:
            return "Tolérable (IPR < 8)"
        elif self.ipr <= 18:
            return "Mineur (8 ≤ IPR ≤ 18)"
        else:
            return "Critique (IPR > 18)"
    
    def update_calculated_fields(self):
        """Met à jour les champs calculés automatiquement"""
        self.ipr = self.calculate_ipr()
        self.niveau_criticite = self.get_niveau_criticite()

class Opportunite(Base):
    __tablename__ = "opportunites"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Identification de l'opportunité
    processus = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=False)
    impact_potentiel = Column(Text)
    
    # Évaluation
    faisabilite = Column(Integer)  # 1-4
    
    # Traitement
    actions = Column(Text)
    responsables = Column(String(200))
    delai = Column(DateTime)
    
    # Mesure d'efficacité
    methode_evaluation = Column(Text)
    date_mesure = Column(DateTime)
    evaluation_efficacite = Column(Text)
    
    # Métadonnées
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class PredictionIA(Base):
    __tablename__ = "predictions_ia"
    
    id = Column(Integer, primary_key=True, index=True)
    risque_id = Column(Integer, nullable=False, index=True)
    
    # Résultats de la prédiction
    niveau_predit = Column(String(50), nullable=False)
    confiance = Column(Float, nullable=False)  # 0.0 - 1.0
    facteurs_influents = Column(JSON)  # Stockage des facteurs qui ont influencé la prédiction
    
    # Métadonnées
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
class ProcessusStats(Base):
    __tablename__ = "processus_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    processus = Column(String(100), nullable=False, unique=True)
    
    # Statistiques
    total_risques = Column(Integer, default=0)
    risques_tolerables = Column(Integer, default=0)
    risques_mineurs = Column(Integer, default=0)
    risques_critiques = Column(Integer, default=0)
    ipr_moyen = Column(Float, default=0.0)
    
    # Métadonnées
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
