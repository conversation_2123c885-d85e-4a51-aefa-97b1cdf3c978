@echo off
echo ========================================
echo   APPLICATION GESTION DES RISQUES
echo ========================================
echo.

echo Verification des dependances...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

echo Python detecte ✓

echo.
echo Demarrage de l'API...
start "API Risques" cmd /k "python start_api_simple.py"

echo Attente du demarrage de l'API...
timeout /t 5 /nobreak >nul

echo.
echo Demarrage de l'interface web...
start "Interface Web" cmd /k "python serve_frontend.py"

echo.
echo ========================================
echo   APPLICATION DEMARREE AVEC SUCCES
echo ========================================
echo.
echo Services disponibles:
echo   Interface Web: http://localhost:3000
echo   API:          http://127.0.0.1:8000
echo   Documentation: http://127.0.0.1:8000/docs
echo.
echo Appuyez sur une touche pour ouvrir l'interface...
pause >nul

start http://localhost:3000

echo.
echo L'application est maintenant accessible.
echo Fermez cette fenetre pour arreter les services.
echo.
pause
