// Configuration de l'API
const API_BASE_URL = 'http://127.0.0.1:8000';

// Variables globales
let risquesData = [];
let processusData = [];

// Fonction Export définie en premier pour être disponible immédiatement
function activerExport() {
    console.log('🔄 Activation de la section export...');

    // Masquer toutes les sections
    const sections = ['dashboard', 'risques-list', 'new-risque', 'prediction-section', 'llama-section', 'methode-cotation-section'];
    sections.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.classList.add('hidden');
    });

    // Afficher la section export
    const exportSection = document.getElementById('export-section');
    if (exportSection) {
        exportSection.classList.remove('hidden');
        console.log('✅ Section export affichée');

        // Charger les filtres d'export
        if (typeof loadExportFilters === 'function') {
            loadExportFilters().catch(error => {
                console.log('Filtres d\'export non disponibles, utilisation des valeurs par défaut');
            });
        }
    } else {
        console.error('❌ Section export non trouvée');
    }
}
let dashboardRefreshInterval;

// Fonctions pour le chatbot Llama
function showLlamaTooltip() {
    const tooltip = document.getElementById('llama-tooltip');
    if (tooltip) {
        tooltip.classList.remove('opacity-0');
        tooltip.classList.add('opacity-100');
    }
}

function hideLlamaTooltip() {
    const tooltip = document.getElementById('llama-tooltip');
    if (tooltip) {
        tooltip.classList.remove('opacity-100');
        tooltip.classList.add('opacity-0');
    }
}



// Configuration Axios
axios.defaults.baseURL = API_BASE_URL;

// Fonctions de navigation
function showDashboard(forceReload = false) {
    hideAllSections();
    document.getElementById('dashboard').classList.remove('hidden');
    loadDashboard(forceReload);

    // Activer le rafraîchissement automatique toutes les 30 secondes
    if (dashboardRefreshInterval) {
        clearInterval(dashboardRefreshInterval);
    }
    dashboardRefreshInterval = setInterval(() => loadDashboard(true), 30000);
}

function showRisques() {
    hideAllSections();
    document.getElementById('risques-list').classList.remove('hidden');
    loadRisques(true); // Forcer le rechargement
    loadProcessusFilter();
}

function showNewRisque() {
    hideAllSections();

    const newRisqueSection = document.getElementById('new-risque');
    if (newRisqueSection) {
        newRisqueSection.classList.remove('hidden');
    } else {
        console.error('Section new-risque non trouvée!');
        return;
    }

    // Réinitialiser le formulaire si ce n'est pas une modification
    if (!currentEditingRisque) {
        resetForm();
    }
}

// Redéfinir showExport pour utiliser activerExport
function showExport() {
    activerExport();
}

// Fonction pour afficher la section IA Avancée
function showAdvancedPrediction() {
    console.log('🤖 Affichage de la section IA Avancée...');

    hideAllSections();

    const advancedSection = document.getElementById('advanced-prediction-section');
    if (advancedSection) {
        advancedSection.classList.remove('hidden');
        console.log('✅ Section IA Avancée affichée');

        // Vérifier le statut des modèles au chargement
        checkAdvancedModelsStatus();
    } else {
        console.error('❌ Section IA Avancée non trouvée');
    }
}

// Fonctions pour l'IA Avancée
async function checkAdvancedModelsStatus() {
    try {
        console.log('🔍 Vérification du statut des modèles avancés...');

        const response = await axios.get('/api/predict/advanced/models/info');
        const status = response.data;

        const statusDiv = document.getElementById('advanced-models-status');

        if (status.status === 'trained') {
            statusDiv.innerHTML = `
                <div class="flex items-center space-x-2 mb-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-green-700 font-medium">Modèles entraînés et prêts</span>
                </div>
                <div class="text-sm text-gray-600">
                    <p>Modèles disponibles: ${status.models_available.join(', ')}</p>
                    <p>Ensemble: ${status.ensemble_available ? 'Oui' : 'Non'}</p>
                    <p>Catégories de features: ${status.feature_categories.length}</p>
                    <p>Total mots-clés: ${status.total_keywords}</p>
                </div>
            `;

            // Charger les performances
            loadModelsPerformance();

        } else {
            statusDiv.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span class="text-red-700 font-medium">Modèles non entraînés</span>
                </div>
                <p class="text-sm text-gray-600 mt-1">Cliquez sur "Entraîner les modèles" pour commencer</p>
            `;
        }

    } catch (error) {
        console.error('Erreur lors de la vérification du statut:', error);

        const statusDiv = document.getElementById('advanced-models-status');
        statusDiv.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span class="text-yellow-700 font-medium">Erreur de connexion</span>
            </div>
            <p class="text-sm text-gray-600 mt-1">Impossible de vérifier le statut des modèles</p>
        `;
    }
}

async function trainAdvancedModels() {
    try {
        console.log('🚀 Démarrage de l\'entraînement des modèles avancés...');

        const statusDiv = document.getElementById('advanced-models-status');
        statusDiv.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span class="text-blue-700 font-medium">Entraînement en cours...</span>
            </div>
            <p class="text-sm text-gray-600 mt-1">Cela peut prendre 2-5 minutes</p>
        `;

        const response = await axios.post('/api/predict/advanced/train');

        if (response.data.status === 'training_started') {
            showSuccess('Entraînement des modèles avancés démarré en arrière-plan');

            // Vérifier le statut toutes les 30 secondes
            const checkInterval = setInterval(async () => {
                try {
                    const statusResponse = await axios.get('/api/predict/advanced/models/info');
                    if (statusResponse.data.status === 'trained') {
                        clearInterval(checkInterval);
                        checkAdvancedModelsStatus();
                        showSuccess('Entraînement terminé avec succès !');
                    }
                } catch (e) {
                    // Continue à vérifier
                }
            }, 30000);

        }

    } catch (error) {
        console.error('Erreur lors de l\'entraînement:', error);
        showError('Erreur lors du démarrage de l\'entraînement');
        checkAdvancedModelsStatus();
    }
}

async function retrainAdvancedModels() {
    try {
        console.log('🔄 Réentraînement des modèles avancés...');

        const response = await axios.post('/api/predict/advanced/retrain', { force: true });

        if (response.data.status === 'retrain_started') {
            showSuccess('Réentraînement démarré en arrière-plan');

            const statusDiv = document.getElementById('advanced-models-status');
            statusDiv.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-orange-500 rounded-full animate-pulse"></div>
                    <span class="text-orange-700 font-medium">Réentraînement en cours...</span>
                </div>
            `;
        }

    } catch (error) {
        console.error('Erreur lors du réentraînement:', error);
        showError('Erreur lors du réentraînement');
    }
}

async function loadModelsPerformance() {
    try {
        const response = await axios.get('/api/predict/advanced/models/performance');
        const performance = response.data;

        if (performance.status === 'trained') {
            const performanceDiv = document.getElementById('models-performance');
            const metrics = performance.performance;

            let html = '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';

            // Performance de l'ensemble
            html += `
                <div class="bg-green-50 p-4 rounded-lg">
                    <h4 class="font-medium text-green-800 mb-2">🏆 Ensemble (Meilleur)</h4>
                    <p class="text-sm">Accuracy: <span class="font-bold">${(metrics.ensemble_accuracy * 100).toFixed(1)}%</span></p>
                    <p class="text-sm">F1-Score: <span class="font-bold">${(metrics.ensemble_f1 * 100).toFixed(1)}%</span></p>
                </div>
            `;

            // Performance des modèles individuels
            if (metrics.individual_models) {
                for (const [modelName, modelMetrics] of Object.entries(metrics.individual_models)) {
                    html += `
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-medium text-blue-800 mb-2">${modelName}</h4>
                            <p class="text-sm">Accuracy: <span class="font-bold">${(modelMetrics.accuracy * 100).toFixed(1)}%</span></p>
                            <p class="text-sm">F1-Score: <span class="font-bold">${(modelMetrics.f1_score * 100).toFixed(1)}%</span></p>
                            <p class="text-sm">CV: ${(modelMetrics.cv_mean * 100).toFixed(1)}% ± ${(modelMetrics.cv_std * 100).toFixed(1)}%</p>
                        </div>
                    `;
                }
            }

            html += '</div>';

            // Informations générales
            html += `
                <div class="mt-4 bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">📊 Informations Générales</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>Échantillons: <span class="font-bold">${metrics.n_samples}</span></div>
                        <div>Features: <span class="font-bold">${metrics.n_features}</span></div>
                        <div>Classes: <span class="font-bold">${metrics.classes.length}</span></div>
                        <div>Meilleurs modèles: <span class="font-bold">${metrics.best_models.join(', ')}</span></div>
                    </div>
                </div>
            `;

            performanceDiv.innerHTML = html;
        }

    } catch (error) {
        console.error('Erreur lors du chargement des performances:', error);
    }
}

// Fonction de prédiction avancée
async function predictAdvanced() {
    try {
        const description = document.getElementById('advanced-description').value;
        const causes = document.getElementById('advanced-causes').value;
        const effets = document.getElementById('advanced-effets').value;
        const processus = document.getElementById('advanced-processus').value;

        if (!description.trim()) {
            showError('Veuillez saisir une description du risque');
            return;
        }

        // Afficher le loading
        document.getElementById('advanced-pred-btn-text').classList.add('hidden');
        document.getElementById('advanced-pred-loading').classList.remove('hidden');

        const requestData = {
            description: description,
            causes: causes,
            effets: effets,
            processus: processus
        };

        const response = await axios.post('/api/predict/advanced/predict', requestData);
        const result = response.data;

        // Masquer le placeholder et afficher les résultats
        document.getElementById('advanced-prediction-placeholder').classList.add('hidden');
        document.getElementById('advanced-prediction-results').classList.remove('hidden');

        // Afficher le niveau prédit
        const levelDiv = document.getElementById('advanced-predicted-level');
        let levelClass = 'bg-green-100 text-green-800';
        if (result.niveau_predit.includes('Critique')) {
            levelClass = 'bg-red-100 text-red-800';
        } else if (result.niveau_predit.includes('Mineur')) {
            levelClass = 'bg-yellow-100 text-yellow-800';
        }

        levelDiv.innerHTML = `<span class="px-3 py-1 rounded-full text-sm font-medium ${levelClass}">${result.niveau_predit}</span>`;

        // Afficher la confiance
        const confidence = Math.round(result.confiance * 100);
        document.getElementById('advanced-confidence-bar').style.width = `${confidence}%`;
        document.getElementById('advanced-confidence-text').textContent = `${confidence}% de confiance`;

        // Afficher le modèle utilisé
        document.getElementById('advanced-model-used').textContent = result.model_used || 'Ensemble ML';

        // Afficher les probabilités
        if (result.probabilites) {
            let probHtml = '<div class="space-y-1">';
            for (const [niveau, prob] of Object.entries(result.probabilites)) {
                const percentage = Math.round(prob * 100);
                probHtml += `
                    <div class="flex justify-between text-sm">
                        <span>${niveau}:</span>
                        <span class="font-medium">${percentage}%</span>
                    </div>
                `;
            }
            probHtml += '</div>';
            document.getElementById('advanced-probabilities').innerHTML = probHtml;
        }

        // Afficher l'analyse des features
        if (result.facteurs_influents) {
            let analysisHtml = '<div class="text-sm space-y-2">';

            if (result.facteurs_influents.text_analysis) {
                const textAnalysis = result.facteurs_influents.text_analysis;
                analysisHtml += `
                    <div><strong>Analyse textuelle:</strong></div>
                    <div class="ml-4">
                        <div>Longueur: ${textAnalysis.length} caractères</div>
                        <div>Mots: ${textAnalysis.word_count}</div>
                        <div>Complexité: ${textAnalysis.complexity?.toFixed(2)}</div>
                    </div>
                `;
            }

            if (result.facteurs_influents.keyword_analysis) {
                analysisHtml += `<div><strong>Mots-clés détectés:</strong></div>`;
                for (const [category, count] of Object.entries(result.facteurs_influents.keyword_analysis)) {
                    if (count > 0) {
                        analysisHtml += `<div class="ml-4">${category}: ${count}</div>`;
                    }
                }
            }

            analysisHtml += '</div>';
            document.getElementById('advanced-feature-analysis').innerHTML = analysisHtml;
        }

        showSuccess('Prédiction avancée terminée avec succès');

    } catch (error) {
        console.error('Erreur lors de la prédiction avancée:', error);
        showError('Erreur lors de la prédiction avancée');

        // Afficher les détails de l'erreur
        if (error.response && error.response.data && error.response.data.detail) {
            showError(`Détail: ${error.response.data.detail}`);
        }
    } finally {
        // Masquer le loading
        document.getElementById('advanced-pred-btn-text').classList.remove('hidden');
        document.getElementById('advanced-pred-loading').classList.add('hidden');
    }
}

// Event listener pour le formulaire de prédiction avancée
document.addEventListener('DOMContentLoaded', function() {
    const advancedForm = document.getElementById('advanced-prediction-form');
    if (advancedForm) {
        advancedForm.addEventListener('submit', function(e) {
            e.preventDefault();
            predictAdvanced();
        });
    }
});



// Fonction de test globale accessible depuis la console
window.testExport = function() {
    console.log('🧪 Test Export depuis la console');
    activerExport();
};

// Fonction showPrediction supprimée - remplacée par showAdvancedPrediction

async function showLlama() {
    hideAllSections();
    document.getElementById('llama-section').classList.remove('hidden');
    await checkLlamaStatus();
}

async function showMethodeCotation() {
    hideAllSections();
    document.getElementById('methode-cotation-section').classList.remove('hidden');
    await loadMethodeCotation();
}

function hideAllSections() {
    const sections = [
        'dashboard',
        'risques-list',
        'new-risque',
        'export-section',
        'advanced-prediction-section',
        'llama-section',
        'methode-cotation-section'
    ];

    sections.forEach(sectionId => {
        const element = document.getElementById(sectionId);
        if (element) {
            element.classList.add('hidden');
        } else {
            console.warn(`Section '${sectionId}' non trouvée dans le DOM`);
        }
    });
}

// Fonctions de chargement des données
async function loadDashboard(forceReload = false) {
    try {
        // Charger les statistiques avec timeout et cache-busting si nécessaire
        const statsUrl = forceReload ? `/api/dashboard/stats?t=${Date.now()}` : '/api/dashboard/stats';
        const statsResponse = await axios.get(statsUrl, { timeout: 10000 });
        const stats = statsResponse.data;

        // Mettre à jour les cartes de statistiques
        document.getElementById('total-risques').textContent = stats.total_risques || 0;
        document.getElementById('risques-critiques').textContent = stats.risques_critiques || 0;
        document.getElementById('risques-mineurs').textContent = stats.risques_mineurs || 0;
        document.getElementById('risques-tolerables').textContent = stats.risques_tolerables || 0;
        document.getElementById('ipr-moyen').textContent = (stats.ipr_moyen || 0).toFixed(1);

        // Supprimer l'animation de chargement
        document.querySelectorAll('.loading').forEach(el => el.classList.remove('loading'));

        // Créer les graphiques avec les données des stats
        setTimeout(() => {
            if (typeof Chart !== 'undefined') {
                try {
                    // Données de criticité depuis les stats
                    const criticiteData = {
                        labels: ['Critique', 'Mineur', 'Tolérable'],
                        data: [stats.risques_critiques, stats.risques_mineurs, stats.risques_tolerables]
                    };

                    // Données de processus depuis les stats
                    const processusData = {
                        labels: stats.processus_stats.map(p => p.processus),
                        data: stats.processus_stats.map(p => p.total_risques)
                    };

                    createCriticiteChart(criticiteData);
                    createProcessusChart(processusData);
                    createCriticiteProcessusChart(stats.processus_stats);
                    createIprProcessusChart(stats.processus_stats);

                } catch (error) {
                    console.error('Erreur lors de la création des graphiques:', error);
                }
            } else {
                console.error('Chart.js non disponible');
            }
        }, 500);

        console.log('Dashboard chargé avec succès');

    } catch (error) {
        console.error('Erreur lors du chargement du dashboard:', error);

        // Afficher des valeurs par défaut
        document.getElementById('total-risques').textContent = '0';
        document.getElementById('risques-critiques').textContent = '0';
        document.getElementById('risques-mineurs').textContent = '0';
        document.getElementById('risques-tolerables').textContent = '0';
        document.getElementById('ipr-moyen').textContent = '0.0';

        showError('Erreur lors du chargement du dashboard. Vérifiez que l\'API est démarrée.');
    }
}


async function loadRisques(forceReload = false) {
    try {
        // Toujours recharger depuis l'API pour avoir les données fraîches
        const response = await axios.get('/api/risques');
        risquesData = response.data;

        // Réinitialiser les filtres si c'est un rechargement forcé
        if (forceReload) {
            resetFilters();
        }

        displayRisques(risquesData);

    } catch (error) {
        console.error('Erreur lors du chargement des risques:', error);
        showError('Erreur lors du chargement des risques');
    }
}

// Fonction pour réinitialiser les filtres
function resetFilters() {
    const processusFilter = document.getElementById('filter-processus');
    const criticiteFilter = document.getElementById('filter-criticite');
    const searchInput = document.getElementById('search-input');

    if (processusFilter) processusFilter.value = '';
    if (criticiteFilter) criticiteFilter.value = '';
    if (searchInput) searchInput.value = '';
}

async function loadProcessusFilter() {
    try {
        const response = await axios.get('/api/risques/processus/list');
        processusData = response.data;
        
        const select = document.getElementById('filter-processus');
        select.innerHTML = '<option value="">Tous les processus</option>';
        
        processusData.forEach(processus => {
            const option = document.createElement('option');
            option.value = processus;
            option.textContent = processus;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Erreur lors du chargement des processus:', error);
    }
}

// Fonctions d'affichage
function displayRisques(risques) {
    const tbody = document.getElementById('risques-tbody');
    if (!tbody) {
        console.error('Élément risques-tbody non trouvé!');
        return;
    }

    tbody.innerHTML = '';

    risques.forEach((risque, index) => {
        try {

            // Vérifier que les données essentielles existent
            if (!risque.id) {
                console.warn(`⚠️ Risque ${index + 1} n'a pas d'ID:`, risque);
                return; // Passer au suivant
            }

            // Analyser la criticité de façon complète
            const niveauCriticite = risque.niveau_criticite || '';
            const criticiteField = risque.criticite || '';
            const ipr = risque.ipr || 0;

            // Déterminer la criticité finale
            let criticiteFinale = 'Non défini';

            // 1. Si IPR > 18, c'est critique
            if (ipr > 18) {
                criticiteFinale = 'Critique';
            }
            // 2. Sinon, utiliser les champs de criticité
            else if (niveauCriticite.toLowerCase().includes('critique')) {
                criticiteFinale = 'Critique';
            }
            else if (criticiteField.toLowerCase().includes('critique')) {
                criticiteFinale = 'Critique';
            }
            else if (niveauCriticite.toLowerCase().includes('mineur')) {
                criticiteFinale = 'Mineur';
            }
            else if (criticiteField.toLowerCase().includes('mineur')) {
                criticiteFinale = 'Mineur';
            }
            else if (niveauCriticite.toLowerCase().includes('tolérable') || niveauCriticite.toLowerCase().includes('tolerable')) {
                criticiteFinale = 'Tolérable';
            }
            else if (criticiteField.toLowerCase().includes('tolérable') || criticiteField.toLowerCase().includes('tolerable')) {
                criticiteFinale = 'Tolérable';
            }
            else if (niveauCriticite || criticiteField) {
                // Garder la valeur originale si elle existe
                criticiteFinale = niveauCriticite || criticiteField;
            }

            risque.criticite_normalized = criticiteFinale;

            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 cursor-pointer';
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${risque.processus || 'N/A'}</td>
                <td class="px-6 py-4 text-sm text-gray-900">
                    <div class="max-w-xs">
                        <div class="font-medium">${(risque.description || '').substring(0, 80)}${(risque.description || '').length > 80 ? '...' : ''}</div>
                        <div class="text-xs text-gray-500 mt-1">
                            <span class="inline-block mr-2">D: ${risque.note_detection || 'N/A'}</span>
                            <span class="inline-block mr-2">G: ${risque.note_gravite || 'N/A'}</span>
                            <span class="inline-block">F: ${risque.note_frequence || 'N/A'}</span>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <span class="font-bold text-lg">${risque.ipr || 'N/A'}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getCriticiteColor(criticiteFinale)}">
                        ${getCriticiteLabel(criticiteFinale)}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button onclick="expandRisque(${risque.id})" class="text-blue-600 hover:text-blue-900" title="Voir détails">
                            Détails
                        </button>
                        <button onclick="editRisque(${risque.id})" class="text-green-600 hover:text-green-900" title="Modifier">
                            Modifier
                        </button>
                        <button onclick="deleteRisque(${risque.id})" class="text-red-600 hover:text-red-900" title="Supprimer">
                            Supprimer
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);

        } catch (error) {
            console.error(`Erreur lors de l'affichage du risque ${index + 1}:`, error);
        }
    });
}



function getCriticiteColor(niveau) {
    if (!niveau) return 'bg-gray-100 text-gray-800';
    const niveauStr = String(niveau).toLowerCase();
    if (niveauStr.includes('critique')) return 'bg-red-100 text-red-800';
    if (niveauStr.includes('mineur')) return 'bg-yellow-100 text-yellow-800';
    if (niveauStr.includes('tolérable') || niveauStr.includes('tolerable')) return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
}





function expandRisque(id) {
    const risque = risquesData.find(r => r.id === id);
    if (!risque) return;

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.id = 'risque-detail-modal';

    modal.innerHTML = `
        <div class="relative top-10 mx-auto p-6 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white max-h-screen overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-xl font-bold text-gray-900">Détails Complets du Risque</h3>
                    <p class="text-sm text-gray-600 mt-1">Toutes les informations selon la méthode AMDEC</p>
                </div>
                <button onclick="closeRisqueDetailModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="sr-only">Fermer</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Résumé en haut -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800">${risque.processus}</h4>
                        <p class="text-gray-600 mt-1">${risque.description}</p>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-gray-800">IPR: ${risque.ipr}</div>
                        <div class="text-sm ${getCriticiteColor(risque.niveau_criticite)} px-3 py-1 rounded-full inline-block mt-1">
                            ${risque.niveau_criticite}
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Identification -->
                <div class="bg-white border rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 border-b border-blue-200 pb-2 mb-4">1. Identification du Risque</h4>
                    <div class="space-y-3">
                        <div><strong>Processus:</strong> <span class="text-gray-700">${risque.processus}</span></div>
                        ${risque.source ? `<div><strong>Source:</strong> <span class="text-gray-700">${risque.source}</span></div>` : '<div class="text-gray-400 italic">Source non renseignée</div>'}
                        ${risque.enjeux ? `<div><strong>Enjeux (internes/externes):</strong> <span class="text-gray-700">${risque.enjeux}</span></div>` : '<div class="text-gray-400 italic">Enjeux non renseignés</div>'}
                        <div><strong>Description du risque:</strong> <span class="text-gray-700">${risque.description}</span></div>
                    </div>
                </div>

                <!-- Analyse -->
                <div class="bg-white border rounded-lg p-4">
                    <h4 class="font-semibold text-green-800 border-b border-green-200 pb-2 mb-4">2. Analyse du Risque</h4>
                    <div class="space-y-3">
                        ${risque.causes ? `<div><strong>Cause(s):</strong> <span class="text-gray-700">${risque.causes}</span></div>` : '<div class="text-gray-400 italic">Causes non renseignées</div>'}
                        ${risque.effets ? `<div><strong>Effet(s):</strong> <span class="text-gray-700">${risque.effets}</span></div>` : '<div class="text-gray-400 italic">Effets non renseignés</div>'}
                    </div>
                </div>

                <!-- Évaluation -->
                <div class="bg-white border rounded-lg p-4">
                    <h4 class="font-semibold text-orange-800 border-b border-orange-200 pb-2 mb-4">3. Évaluation du Risque</h4>
                    <div class="space-y-4">
                        ${risque.moyens_detection ? `<div><strong>Moyen de détection:</strong> <span class="text-gray-700">${risque.moyens_detection}</span></div>` : '<div class="text-gray-400 italic">Moyen de détection non renseigné</div>'}

                        <div class="bg-gray-50 p-4 rounded">
                            <h5 class="font-medium mb-3 text-center">Cotation AMDEC</h5>
                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">${risque.note_detection}</div>
                                    <div class="text-xs text-gray-600">Détection</div>
                                    <div class="text-xs text-gray-500 mt-1">(1=Facile à 5=Difficile)</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-red-600">${risque.note_gravite}</div>
                                    <div class="text-xs text-gray-600">Gravité</div>
                                    <div class="text-xs text-gray-500 mt-1">(1=Négligeable à 5=Catastrophique)</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-600">${risque.note_frequence}</div>
                                    <div class="text-xs text-gray-600">Fréquence</div>
                                    <div class="text-xs text-gray-500 mt-1">(1=Très rare à 5=Fréquent)</div>
                                </div>
                            </div>
                            <div class="text-center mt-4 p-3 bg-white rounded border-2 border-gray-300">
                                <div class="text-sm text-gray-600">Indice de Priorité du Risque</div>
                                <div class="text-3xl font-bold text-gray-800">IPR = ${risque.ipr}</div>
                                <div class="text-xs text-gray-500 mt-1">(Détection × Gravité × Fréquence)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Traitement -->
                <div class="bg-white border rounded-lg p-4">
                    <h4 class="font-semibold text-purple-800 border-b border-purple-200 pb-2 mb-4">4. Traitement du Risque</h4>
                    <div class="space-y-3">
                        ${risque.moyens_maitrise ? `<div><strong>Moyen de maîtrise:</strong> <span class="text-gray-700">${risque.moyens_maitrise}</span></div>` : '<div class="text-gray-400 italic">Moyen de maîtrise non renseigné</div>'}
                        ${risque.actions_risques ? `<div><strong>Actions face aux risques:</strong> <span class="text-gray-700">${risque.actions_risques}</span></div>` : '<div class="text-gray-400 italic">Actions non renseignées</div>'}
                        ${risque.responsables ? `<div><strong>Responsable(s):</strong> <span class="text-gray-700">${risque.responsables}</span></div>` : '<div class="text-gray-400 italic">Responsables non renseignés</div>'}
                        ${risque.delai_realisation ? `<div><strong>Délai de réalisation:</strong> <span class="text-gray-700">${risque.delai_realisation}</span></div>` : '<div class="text-gray-400 italic">Délai non renseigné</div>'}
                    </div>
                </div>
            </div>

            <!-- Efficacité (pleine largeur) -->
            <div class="bg-white border rounded-lg p-4 mt-6">
                <h4 class="font-semibold text-indigo-800 border-b border-indigo-200 pb-2 mb-4">5. Mesure d'Efficacité</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <strong>Méthode d'évaluation:</strong>
                        <div class="text-gray-700 mt-1">${risque.methode_evaluation || '<span class="text-gray-400 italic">Non renseignée</span>'}</div>
                    </div>
                    <div>
                        <strong>Date de mesure:</strong>
                        <div class="text-gray-700 mt-1">${risque.date_mesure || '<span class="text-gray-400 italic">Non renseignée</span>'}</div>
                    </div>
                    <div>
                        <strong>Évaluation de l'efficacité:</strong>
                        <div class="text-gray-700 mt-1">${risque.evaluation_efficacite || '<span class="text-gray-400 italic">Non renseignée</span>'}</div>
                    </div>
                </div>
            </div>
            </div>

            <div class="flex justify-end space-x-2 mt-6 pt-4 border-t">
                <button onclick="editRisque(${risque.id})" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Modifier
                </button>
                <button onclick="closeRisqueDetailModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Fermer
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function closeRisqueDetailModal() {
    const modal = document.getElementById('risque-detail-modal');
    if (modal) {
        modal.remove();
    }
}

function getCriticiteLabel(niveau) {
    if (!niveau) return 'Non défini';
    const niveauStr = String(niveau).toLowerCase();
    if (niveauStr.includes('critique')) return 'Critique';
    if (niveauStr.includes('mineur')) return 'Mineur';
    if (niveauStr.includes('tolérable') || niveauStr.includes('tolerable')) return 'Tolérable';
    return niveau; // Retourner la valeur originale si pas de correspondance
}

// Fonctions de graphiques
function createCriticiteChart(data) {
    // Vérifier que Chart.js est disponible
    if (typeof Chart === 'undefined') {
        console.error('Chart.js non disponible pour createCriticiteChart');
        return;
    }

    const canvas = document.getElementById('criticiteChart');
    if (!canvas) {
        console.error('Canvas criticiteChart non trouvé');
        return;
    }

    const ctx = canvas.getContext('2d');

    // Détruire le graphique existant s'il existe
    if (window.criticiteChart && typeof window.criticiteChart.destroy === 'function') {
        window.criticiteChart.destroy();
    }

    window.criticiteChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels.map(label => getCriticiteLabel(label)),
            datasets: [{
                data: data.data,
                backgroundColor: data.labels.map(label => {
                    if (label.includes('Critique')) return '#EF4444';
                    if (label.includes('Mineur')) return '#F59E0B';
                    return '#10B981';
                }),
                borderWidth: 2,
                borderColor: '#ffffff',
                hoverBorderWidth: 3,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        generateLabels: function(chart) {
                            const data = chart.data;
                            if (data.labels.length && data.datasets.length) {
                                return data.labels.map((label, i) => {
                                    const value = data.datasets[0].data[i];
                                    const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return {
                                        text: `${label} (${percentage}%)`,
                                        fillStyle: data.datasets[0].backgroundColor[i],
                                        strokeStyle: data.datasets[0].backgroundColor[i],
                                        lineWidth: 0,
                                        pointStyle: 'circle',
                                        hidden: false,
                                        index: i
                                    };
                                });
                            }
                            return [];
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#ffffff',
                    borderWidth: 1,
                    cornerRadius: 6,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} risques (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%',
            radius: '90%'
        }
    });

    console.log('Graphique criticité créé avec succès');
}

function createProcessusChart(data) {
    // Vérifier que Chart.js est disponible
    if (typeof Chart === 'undefined') {
        console.error('Chart.js non disponible pour createProcessusChart');
        return;
    }

    const canvas = document.getElementById('processusChart');
    if (!canvas) {
        console.error('Canvas processusChart non trouvé');
        return;
    }

    const ctx = canvas.getContext('2d');

    // Détruire le graphique existant s'il existe
    if (window.processusChart && typeof window.processusChart.destroy === 'function') {
        window.processusChart.destroy();
    }

    window.processusChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: [{
                label: 'Nombre de risques',
                data: data.data,
                backgroundColor: '#3B82F6',
                borderColor: '#1E40AF',
                borderWidth: 1,
                borderRadius: 4,
                borderSkipped: false,
                hoverBackgroundColor: '#2563EB',
                hoverBorderColor: '#1D4ED8',
                hoverBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1200,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#3B82F6',
                    borderWidth: 1,
                    cornerRadius: 6,
                    callbacks: {
                        title: function(context) {
                            return `Processus: ${context[0].label}`;
                        },
                        label: function(context) {
                            const value = context.parsed.y;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${value} risques (${percentage}% du total)`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        font: {
                            size: 11,
                            weight: '500'
                        },
                        color: '#374151'
                    },
                    grid: {
                        color: '#E5E7EB',
                        lineWidth: 1
                    },
                    title: {
                        display: true,
                        text: 'Nombre de risques',
                        font: {
                            size: 12,
                            weight: 'bold'
                        },
                        color: '#374151'
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0,
                        font: {
                            size: 10,
                            weight: '500'
                        },
                        color: '#374151'
                    },
                    grid: {
                        display: false
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    console.log('Graphique processus créé avec succès');
}

function createCriticiteProcessusChart(processusStats) {
    if (typeof Chart === 'undefined') {
        console.error('Chart.js non disponible pour createCriticiteProcessusChart');
        return;
    }

    const canvas = document.getElementById('criticiteProcessusChart');
    if (!canvas) {
        console.error('Canvas criticiteProcessusChart non trouvé');
        return;
    }

    const ctx = canvas.getContext('2d');

    // Détruire le graphique existant s'il existe
    if (window.criticiteProcessusChart && typeof window.criticiteProcessusChart.destroy === 'function') {
        window.criticiteProcessusChart.destroy();
    }

    // Préparer les données
    const labels = processusStats.map(p => p.processus);
    const critiques = processusStats.map(p => p.risques_critiques || 0);
    const mineurs = processusStats.map(p => p.risques_mineurs || 0);
    const tolerables = processusStats.map(p => p.risques_tolerables || 0);

    window.criticiteProcessusChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Critiques',
                    data: critiques,
                    backgroundColor: '#EF4444',
                    borderColor: '#DC2626',
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                },
                {
                    label: 'Mineurs',
                    data: mineurs,
                    backgroundColor: '#F59E0B',
                    borderColor: '#D97706',
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                },
                {
                    label: 'Tolérables',
                    data: tolerables,
                    backgroundColor: '#10B981',
                    borderColor: '#059669',
                    borderWidth: 1,
                    borderRadius: 4,
                    borderSkipped: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1200,
                easing: 'easeOutQuart'
            },
            scales: {
                x: {
                    stacked: true,
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    grid: {
                        color: '#F3F4F6'
                    },
                    ticks: {
                        stepSize: 1,
                        font: {
                            size: 11
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#374151',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.parsed.y;
                            return `${label}: ${value} risque${value > 1 ? 's' : ''}`;
                        }
                    }
                }
            }
        }
    });

    console.log('Graphique criticité par processus créé avec succès');
}

function createIprProcessusChart(processusStats) {
    if (typeof Chart === 'undefined') {
        console.error('Chart.js non disponible pour createIprProcessusChart');
        return;
    }

    const canvas = document.getElementById('iprProcessusChart');
    if (!canvas) {
        console.error('Canvas iprProcessusChart non trouvé');
        return;
    }

    const ctx = canvas.getContext('2d');

    // Détruire le graphique existant s'il existe
    if (window.iprProcessusChart && typeof window.iprProcessusChart.destroy === 'function') {
        window.iprProcessusChart.destroy();
    }

    // Préparer les données
    const labels = processusStats.map(p => p.processus);
    const iprData = processusStats.map(p => parseFloat(p.ipr_moyen || 0));

    // Couleurs dégradées basées sur l'IPR
    const backgroundColors = iprData.map(ipr => {
        if (ipr > 18) return '#DC2626'; // Rouge foncé pour critique
        if (ipr >= 8) return '#D97706';  // Orange foncé pour mineur
        return '#059669';               // Vert foncé pour tolérable
    });

    const borderColors = iprData.map(ipr => {
        if (ipr > 18) return '#B91C1C'; // Rouge plus foncé
        if (ipr >= 8) return '#B45309';  // Orange plus foncé
        return '#047857';               // Vert plus foncé
    });

    window.iprProcessusChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'IPR Moyen',
                data: iprData,
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                pointBackgroundColor: backgroundColors,
                pointBorderColor: borderColors,
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                pointHoverBackgroundColor: backgroundColors,
                pointHoverBorderColor: borderColors,
                pointHoverBorderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1200,
                easing: 'easeOutQuart'
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#374151'
                    }
                },
                y: {
                    beginAtZero: true,
                    max: Math.max(Math.max(...iprData) * 1.3, 20),
                    grid: {
                        color: '#E5E7EB',
                        lineWidth: 1
                    },
                    ticks: {
                        font: {
                            size: 12,
                            weight: '500'
                        },
                        color: '#374151',
                        callback: function(value) {
                            return value.toFixed(1);
                        }
                    },
                    title: {
                        display: true,
                        text: 'Indice de Priorité du Risque (IPR)',
                        font: {
                            size: 13,
                            weight: '600'
                        },
                        color: '#1F2937'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#374151',
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            const value = parseFloat(context.parsed.y);
                            let niveau = 'Tolérable';
                            if (value > 18) niveau = 'Critique';
                            else if (value >= 8) niveau = 'Mineur';

                            return [
                                `IPR Moyen: ${value}`,
                                `Niveau: ${niveau}`
                            ];
                        }
                    }
                }
            }
        }
    });

    console.log('Graphique IPR par processus créé avec succès');
}

// Fonctions de filtrage
function filterRisques() {
    const processusFilter = document.getElementById('filter-processus').value;
    const criticiteFilter = document.getElementById('filter-criticite').value;
    const searchFilter = document.getElementById('search-input').value.toLowerCase();
    
    let filteredRisques = risquesData;
    
    if (processusFilter) {
        filteredRisques = filteredRisques.filter(r => r.processus === processusFilter);
    }
    
    if (criticiteFilter) {
        filteredRisques = filteredRisques.filter(r => {
            const criticite = r.niveau_criticite || r.criticite || '';
            return criticite === criticiteFilter;
        });
    }
    
    if (searchFilter) {
        filteredRisques = filteredRisques.filter(r => 
            r.description.toLowerCase().includes(searchFilter) ||
            r.causes?.toLowerCase().includes(searchFilter) ||
            r.effets?.toLowerCase().includes(searchFilter)
        );
    }
    
    displayRisques(filteredRisques);
}

// Fonctions de gestion des risques
function calculateIPR() {
    const detection = parseInt(document.getElementById('note_detection').value) || 0;
    const gravite = parseInt(document.getElementById('note_gravite').value) || 0;
    const frequence = parseInt(document.getElementById('note_frequence').value) || 0;

    const ipr = detection * gravite * frequence;
    document.getElementById('ipr-display').value = ipr || '';
}

function calculateRiskLevel() {
    try {
        const detection = parseInt(document.getElementById('note_detection').value) || 0;
        const gravite = parseInt(document.getElementById('note_gravite').value) || 0;
        const frequence = parseInt(document.getElementById('note_frequence').value) || 0;

        if (!detection || !gravite || !frequence) {
            let missingFields = [];
            if (!detection) missingFields.push('Détection');
            if (!gravite) missingFields.push('Gravité');
            if (!frequence) missingFields.push('Fréquence');

            showError(`Veuillez renseigner les notes manquantes : ${missingFields.join(', ')}`);
            return;
        }

        const ipr = detection * gravite * frequence;

        // Calcul du niveau selon les règles AMDEC
        let niveau, couleur;
        if (ipr > 18) {
            niveau = "Critique (IPR > 18)";
            couleur = "bg-red-100 text-red-800";
        } else if (ipr >= 8) {
            niveau = "Mineur (8 ≤ IPR ≤ 18)";
            couleur = "bg-yellow-100 text-yellow-800";
        } else {
            niveau = "Tolérable (IPR < 8)";
            couleur = "bg-green-100 text-green-800";
        }

        // Affichage du résultat
        const resultDiv = document.getElementById('risk-level-result');
        const levelSpan = document.getElementById('calculated-risk-level');

        if (resultDiv && levelSpan) {
            levelSpan.textContent = niveau;
            levelSpan.className = `px-2 py-1 rounded text-sm font-medium ${couleur}`;
            resultDiv.classList.remove('hidden');
        }

        // Mise à jour automatique de l'IPR
        const iprDisplay = document.getElementById('ipr-display');
        if (iprDisplay) {
            iprDisplay.value = ipr;
        }

    } catch (error) {
        showError('Erreur lors du calcul du niveau de criticité');
    }
}

async function createRisque(formData) {
    console.log('createRisque appelée avec:', formData);

    // Validation des données
    if (!formData.processus || !formData.description) {
        showError('Processus et description sont requis');
        return;
    }

    if (!formData.note_detection || !formData.note_gravite || !formData.note_frequence) {
        showError('Toutes les notes (détection, gravité, fréquence) sont requises');
        return;
    }

    try {
        let response;

        if (currentEditingRisque) {
            // Mode modification
            console.log('Mode modification, ID:', currentEditingRisque);
            response = await axios.put(`/api/risques/${currentEditingRisque}`, formData);
            showSuccess('✅ Risque modifié avec succès!');
        } else {
            // Mode création
            console.log('Mode création');
            response = await axios.post('/api/risques', formData);
            showSuccess('✅ Risque créé avec succès!');
        }

        console.log('Réponse API:', response.data);

        // Réinitialiser le formulaire
        const form = document.getElementById('risque-form');
        if (form) {
            form.reset();
        }
        resetForm();

        // Forcer le rechargement des données
        await refreshAllData();

        // Retourner au dashboard avec rechargement forcé
        showDashboard(true);

        // Message de confirmation supplémentaire
        setTimeout(() => {
            console.log('✅ Toutes les données ont été rafraîchies');
        }, 1000);

    } catch (error) {
        console.error('Erreur lors de la sauvegarde du risque:', error);
        const errorMessage = error.response?.data?.detail || error.message || 'Erreur inconnue';
        showError('❌ Erreur: ' + errorMessage);
    }
}

function resetForm() {
    // Réinitialiser les variables de modification
    currentEditingRisque = null;

    // Réinitialiser le titre et le bouton avec vérifications
    const titleElement = document.querySelector('#new-risque h2');
    if (titleElement) {
        titleElement.textContent = 'Nouveau Risque';
    }

    const submitButton = document.querySelector('#risque-form button[type="submit"]');
    if (submitButton) {
        submitButton.textContent = 'Créer le Risque';
    }

    // Masquer la prédiction (si elle existe)
    const predictionResult = document.getElementById('risk-prediction-result');
    if (predictionResult) {
        predictionResult.classList.add('hidden');
    }

    // Réinitialiser l'IPR (si l'élément existe)
    const iprDisplay = document.getElementById('ipr-display');
    if (iprDisplay) {
        iprDisplay.value = '';
    }

    // Réinitialiser les nouveaux champs (avec vérifications)
    const moyenDetection = document.getElementById('moyen_detection');
    if (moyenDetection) {
        moyenDetection.value = '';
    }

    const moyenMaitrise = document.getElementById('moyen_maitrise');
    if (moyenMaitrise) {
        moyenMaitrise.value = '';
    }
}

// Variables globales pour la gestion des risques
let expandedRisques = new Set();
let currentEditingRisque = null;

// Fonction pour rafraîchir toutes les données
async function refreshAllData() {
    try {
        // Vider le cache des données
        risquesData = [];
        processusData = [];

        // Recharger les données depuis l'API
        const risquesResponse = await axios.get('/api/risques');
        risquesData = risquesResponse.data;

        // Si on est sur la page des risques, la rafraîchir
        const risquesList = document.getElementById('risques-list');
        if (risquesList && !risquesList.classList.contains('hidden')) {
            await loadRisques();
        }

    } catch (error) {
        console.error('Erreur lors du rafraîchissement des données:', error);
    }
}

function expandRisque(id) {
    if (expandedRisques.has(id)) {
        // Réduire le risque
        expandedRisques.delete(id);
        const detailRow = document.getElementById(`detail-${id}`);
        if (detailRow) {
            detailRow.remove();
        }
    } else {
        // Étendre le risque
        expandedRisques.add(id);
        showRisqueDetails(id);
    }
}

async function showRisqueDetails(id) {
    try {
        const response = await axios.get(`/api/risques/${id}`);
        const risque = response.data;

        // Trouver la ligne du risque
        const rows = document.querySelectorAll('#risques-tbody tr');
        let targetRow = null;

        rows.forEach(row => {
            const buttons = row.querySelectorAll('button');
            buttons.forEach(button => {
                if (button.onclick && button.onclick.toString().includes(`expandRisque(${id})`)) {
                    targetRow = row;
                }
            });
        });

        if (targetRow) {
            // Créer la ligne de détails
            const detailRow = document.createElement('tr');
            detailRow.id = `detail-${id}`;
            detailRow.className = 'bg-blue-50';
            detailRow.innerHTML = `
                <td colspan="5" class="px-6 py-4">
                    <div class="bg-white rounded-lg p-4 shadow-sm">
                        <h4 class="font-bold text-lg mb-3 text-blue-800">Détails du Risque #${id}</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="font-semibold text-gray-700 mb-2">Informations Générales</h5>
                                <p><strong>Processus:</strong> ${risque.processus}</p>
                                <p><strong>Source:</strong> ${risque.source || 'Non spécifiée'}</p>
                                <p><strong>Description:</strong> ${risque.description}</p>
                                <p><strong>Causes:</strong> ${risque.causes || 'Non spécifiées'}</p>
                                <p><strong>Effets:</strong> ${risque.effets || 'Non spécifiés'}</p>
                            </div>
                            <div>
                                <h5 class="font-semibold text-gray-700 mb-2">Évaluation</h5>
                                <div class="grid grid-cols-3 gap-2 mb-2">
                                    <div class="text-center p-2 bg-gray-100 rounded">
                                        <div class="text-sm text-gray-600">Détection</div>
                                        <div class="text-xl font-bold">${risque.note_detection}</div>
                                    </div>
                                    <div class="text-center p-2 bg-gray-100 rounded">
                                        <div class="text-sm text-gray-600">Gravité</div>
                                        <div class="text-xl font-bold">${risque.note_gravite}</div>
                                    </div>
                                    <div class="text-center p-2 bg-gray-100 rounded">
                                        <div class="text-sm text-gray-600">Fréquence</div>
                                        <div class="text-xl font-bold">${risque.note_frequence}</div>
                                    </div>
                                </div>
                                <div class="text-center p-3 bg-blue-100 rounded">
                                    <div class="text-sm text-blue-600">IPR Calculé</div>
                                    <div class="text-2xl font-bold text-blue-800">${risque.ipr}</div>
                                    <div class="text-sm text-blue-600">${risque.niveau_criticite}</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h5 class="font-semibold text-gray-700 mb-2">Moyens de Contrôle</h5>
                            <p><strong>Moyen de détection:</strong> ${risque.moyens_detection || 'Non spécifié'}</p>
                            <p><strong>Moyen de maîtrise:</strong> ${risque.moyens_maitrise || 'Non spécifié'}</p>
                        </div>
                        <div class="mt-4">
                            <h5 class="font-semibold text-gray-700 mb-2">Actions et Responsabilités</h5>
                            <p><strong>Actions:</strong> ${risque.actions || 'Non spécifiées'}</p>
                            <p><strong>Responsables:</strong> ${risque.responsables || 'Non assignés'}</p>
                            <p><strong>Délai:</strong> ${risque.delai ? new Date(risque.delai).toLocaleDateString() : 'Non défini'}</p>
                        </div>
                        <div class="mt-4 flex justify-end space-x-2">
                            <button onclick="editRisque(${id})" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                Modifier
                            </button>
                            <button onclick="expandRisque(${id})" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                                Fermer
                            </button>
                        </div>
                    </div>
                </td>
            `;

            // Insérer la ligne de détails après la ligne du risque
            targetRow.parentNode.insertBefore(detailRow, targetRow.nextSibling);
        }

    } catch (error) {
        console.error('Erreur lors du chargement des détails:', error);
        showError('Erreur lors du chargement des détails du risque');
    }
}

async function editRisque(id) {
    try {
        const response = await axios.get(`/api/risques/${id}`);
        const risque = response.data;

        // Remplir le formulaire de modification
        currentEditingRisque = id;

        // Basculer vers la section nouveau risque
        showNewRisque();

        // Remplir les champs
        document.getElementById('processus').value = risque.processus;
        document.getElementById('source').value = risque.source || '';
        document.getElementById('description').value = risque.description;
        document.getElementById('causes').value = risque.causes || '';
        document.getElementById('effets').value = risque.effets || '';
        document.getElementById('moyen_detection').value = risque.moyens_detection || '';
        document.getElementById('moyen_maitrise').value = risque.moyens_maitrise || '';
        document.getElementById('note_detection').value = risque.note_detection;
        document.getElementById('note_gravite').value = risque.note_gravite;
        document.getElementById('note_frequence').value = risque.note_frequence;
        document.getElementById('actions_risques').value = risque.actions_risques || '';
        document.getElementById('responsables').value = risque.responsables || '';

        // Calculer l'IPR
        calculateIPR();

        // Changer le titre et le bouton
        document.querySelector('#new-risque h2').textContent = `Modifier le Risque #${id}`;
        document.querySelector('#risque-form button[type="submit"]').textContent = 'Mettre à jour le Risque';

        showSuccess('Risque chargé pour modification');

    } catch (error) {
        console.error('Erreur lors du chargement du risque:', error);
        showError('Erreur lors du chargement du risque pour modification');
    }
}

async function deleteRisque(id) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce risque ? Cette action est irréversible.')) {
        return;
    }

    try {
        const response = await axios.delete(`/api/risques/${id}`);

        if (response.status === 200) {
            showSuccess('Risque supprimé avec succès');

            // Recharger la liste des risques
            loadRisques();

            // Supprimer de la liste des risques étendus
            expandedRisques.delete(id);

        } else {
            showError('Erreur lors de la suppression du risque');
        }

    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        showError('Erreur lors de la suppression du risque');
    }
}

// Fonctions utilitaires
function showError(message) {
    // Créer une notification d'erreur simple
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded shadow-lg z-50';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 5000);
}

function showSuccess(message) {
    // Créer une notification de succès simple
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded shadow-lg z-50';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        document.body.removeChild(notification);
    }, 5000);
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter l'event listener Export en premier
    const exportBtn = document.getElementById('export-btn');
    if (exportBtn) {
        exportBtn.addEventListener('click', activerExport);
        console.log('✅ Event listener Export ajouté');
    }

    // Charger le dashboard par défaut
    showDashboard();

    // Event listeners pour le calcul automatique de l'IPR
    ['note_detection', 'note_gravite', 'note_frequence'].forEach(id => {
        document.getElementById(id).addEventListener('change', calculateIPR);
    });
    
    // Event listener pour le formulaire de création de risque
    const risqueForm = document.getElementById('risque-form');
    if (risqueForm) {
        risqueForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Formulaire soumis!');

            // Vérifier que tous les champs requis existent
            const processus = document.getElementById('processus');
            const description = document.getElementById('description');
            const noteDetection = document.getElementById('note_detection');
            const noteGravite = document.getElementById('note_gravite');
            const noteFrequence = document.getElementById('note_frequence');

            if (!processus || !description || !noteDetection || !noteGravite || !noteFrequence) {
                console.error('Champs requis manquants dans le formulaire');
                showError('Erreur: Champs requis manquants');
                return;
            }

            const formData = {
                processus: processus.value,
                source: document.getElementById('source')?.value || null,
                description: description.value,
                causes: document.getElementById('causes')?.value || null,
                effets: document.getElementById('effets')?.value || null,
                moyens_detection: document.getElementById('moyen_detection')?.value || null,
                moyens_maitrise: document.getElementById('moyen_maitrise')?.value || null,
                note_detection: parseInt(noteDetection.value),
                note_gravite: parseInt(noteGravite.value),
                note_frequence: parseInt(noteFrequence.value),
                actions_risques: document.getElementById('actions_risques')?.value || null,
                responsables: document.getElementById('responsables')?.value || null
            };

            console.log('Données du formulaire:', formData);
            createRisque(formData);
        });
    } else {
        console.error('Formulaire risque-form non trouvé!');
    }
    
    // Event listeners pour les filtres
    document.getElementById('search-input').addEventListener('input', filterRisques);

    // Event listener pour le formulaire d'export
    document.getElementById('export-form').addEventListener('submit', function(e) {
        e.preventDefault();
        generateReport();
    });

    // Event listener pour le formulaire de prédiction
    // Event listener pour l'ancienne prédiction supprimé - remplacé par l'IA Avancée

    // Event listener pour le bouton Export - Approche directe
    setTimeout(() => {
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            console.log('🔄 Ajout de l\'event listener Export...');

            exportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔄 Event listener Export déclenché');
                activerExport();
            });

            // Test immédiat
            exportBtn.style.cursor = 'pointer';
            exportBtn.title = 'Cliquez pour accéder à l\'export';

            console.log('✅ Event listener Export ajouté avec succès');

            // Vérification
            console.log('Vérification du bouton:', exportBtn);
        } else {
            console.error('❌ Bouton export-btn non trouvé après timeout');
        }
    }, 1000); // Attendre 1 seconde pour s'assurer que le DOM est prêt
});

// Fonctions d'export
async function loadExportFilters() {
    try {
        console.log('🔄 Chargement des filtres d\'export...');

        // Charger les processus depuis les données des risques
        const processusSelect = document.getElementById('export-processus');
        if (processusSelect) {
            processusSelect.innerHTML = '<option value="">Tous les processus</option>';

            // Charger les risques si pas encore fait
            if (!risquesData || risquesData.length === 0) {
                try {
                    const risquesResponse = await axios.get('/api/risques');
                    risquesData = risquesResponse.data;
                } catch (error) {
                    console.log('Impossible de charger les risques pour les filtres');
                }
            }

            // Extraire les processus uniques des risques
            if (risquesData && risquesData.length > 0) {
                const processusUniques = [...new Set(risquesData.map(r => r.processus).filter(p => p))];
                processusUniques.sort(); // Trier alphabétiquement

                processusUniques.forEach(processus => {
                    const option = document.createElement('option');
                    option.value = processus;
                    option.textContent = processus;
                    processusSelect.appendChild(option);
                });

                console.log(`✅ ${processusUniques.length} processus chargés dans les filtres d'export`);
            } else {
                // Utiliser les processus par défaut si pas de données
                const processusDefaut = [
                    'Management de l\'entreprise',
                    'Production',
                    'Commercial',
                    'Gestion des ressources humaines',
                    'Infrastructures et IT',
                    'Customer service',
                    'Achat',
                    'Facturation et recouvrement'
                ];

                processusDefaut.forEach(processus => {
                    const option = document.createElement('option');
                    option.value = processus;
                    option.textContent = processus;
                    processusSelect.appendChild(option);
                });

                console.log('✅ Processus par défaut chargés dans les filtres d\'export');
            }
        }

    } catch (error) {
        console.error('Erreur lors du chargement des filtres d\'export:', error);
    }
}

async function generateReport() {
    const format = document.getElementById('export-format').value;

    if (!format) {
        showError('Veuillez sélectionner un format d\'export');
        return;
    }

    // Afficher le loading
    document.getElementById('export-btn-text').classList.add('hidden');
    document.getElementById('export-loading').classList.remove('hidden');
    document.getElementById('download-section').classList.add('hidden');

    try {
        // Récupérer le type de rapport
        const templateElement = document.getElementById('export-template');
        const template = templateElement ? templateElement.value : 'standard';

        console.log('🔍 Template sélectionné:', template);
        console.log('🔍 Élément template:', templateElement);

        // Préparer les données d'export
        const exportData = {
            format: format,
            template: template, // Ajouter le type de rapport
            processus_filter: getSelectedProcessus(),
            criticite_filter: getSelectedCriticite(),
            date_debut: document.getElementById('export-date-debut').value || null,
            date_fin: document.getElementById('export-date-fin').value || null,
            include_opportunites: false
        };

        console.log('📄 Génération du rapport:', template === 'summary' ? 'Résumé Exécutif' : 'Rapport Standard');
        console.log('📤 Données envoyées:', exportData);

        // Appeler l'API d'export
        const endpoint = format === 'pdf' ? '/api/export/pdf' : '/api/export/word';
        const response = await axios.post(endpoint, exportData);

        // Afficher le lien de téléchargement
        const downloadLink = document.getElementById('download-link');
        downloadLink.href = `http://127.0.0.1:8000/api/export/download/${response.data.filename}`;
        downloadLink.download = response.data.filename;

        document.getElementById('download-section').classList.remove('hidden');
        showSuccess('Rapport généré avec succès!');

    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        showError('Erreur lors de la génération du rapport');
    } finally {
        // Masquer le loading
        document.getElementById('export-btn-text').classList.remove('hidden');
        document.getElementById('export-loading').classList.add('hidden');
    }
}

function getSelectedProcessus() {
    const select = document.getElementById('export-processus');
    const selected = [];

    for (let option of select.selectedOptions) {
        selected.push(option.value);
    }

    return selected.length > 0 ? selected : null;
}

function getSelectedCriticite() {
    const checkboxes = document.querySelectorAll('.export-criticite:checked');
    const selected = [];

    checkboxes.forEach(checkbox => {
        selected.push(checkbox.value);
    });

    return selected.length > 0 ? selected : null;
}

// Variables globales pour la prédiction
let predictionExamples = [];

// Fonctions d'exemples adaptées pour l'IA Avancée
async function loadPredictionExamples() {
    try {
        const response = await axios.get('/api/predict/examples');
        predictionExamples = response.data.examples;
    } catch (error) {
        console.error('Erreur lors du chargement des exemples:', error);
        // Exemples par défaut si l'API échoue
        predictionExamples = [
            {
                processus: "Infrastructures et IT",
                description: "Panne critique du serveur principal causant un arrêt total de la production",
                causes: "Défaillance matérielle, absence de redondance",
                effets: "Arrêt complet de la production, perte de données"
            },
            {
                processus: "Facturation et recouvrement",
                description: "Erreurs occasionnelles dans le calcul des factures clients",
                causes: "Configuration incorrecte du système",
                effets: "Réclamations clients, temps de correction"
            },
            {
                processus: "Gestion des ressources humaines",
                description: "Formation du personnel pas toujours à jour",
                causes: "Budget formation limité, planning chargé",
                effets: "Légère baisse de productivité, adaptation plus lente aux nouveautés"
            }
        ];
    }
}

async function predictRiskLevel() {
    const description = document.getElementById('pred-description').value;
    const causes = document.getElementById('pred-causes').value;
    const effets = document.getElementById('pred-effets').value;
    const processus = document.getElementById('pred-processus').value;

    if (!description.trim()) {
        showError('Veuillez saisir une description du risque');
        return;
    }

    // Afficher le loading
    document.getElementById('pred-btn-text').classList.add('hidden');
    document.getElementById('pred-loading').classList.remove('hidden');

    try {
        const predictionData = {
            description: description,
            causes: causes || null,
            effets: effets || null,
            processus: processus || null
        };

        const response = await axios.post('/api/predict/risque', predictionData);
        const prediction = response.data;

        displayPredictionResults(prediction);

    } catch (error) {
        console.error('Erreur lors de la prédiction:', error);
        showError('Erreur lors de la prédiction IA');
    } finally {
        // Masquer le loading
        document.getElementById('pred-btn-text').classList.remove('hidden');
        document.getElementById('pred-loading').classList.add('hidden');
    }
}

function displayPredictionResults(prediction) {
    // Masquer le placeholder et afficher les résultats
    document.getElementById('prediction-placeholder').classList.add('hidden');
    document.getElementById('prediction-results').classList.remove('hidden');

    // Afficher le niveau prédit
    const levelElement = document.getElementById('predicted-level');
    levelElement.textContent = getLevelLabel(prediction.niveau_predit);
    levelElement.className = `text-2xl font-bold p-3 rounded text-center ${getLevelColor(prediction.niveau_predit)}`;

    // Afficher la confiance
    const confidence = Math.round(prediction.confiance * 100);
    document.getElementById('confidence-bar').style.width = `${confidence}%`;
    document.getElementById('confidence-text').textContent = `${confidence}% de confiance`;

    // Afficher les facteurs influents
    const factorsElement = document.getElementById('influence-factors');
    factorsElement.innerHTML = '';

    const factors = prediction.facteurs_influents;

    // Type de modèle utilisé
    if (factors.model_type) {
        const modelFactor = document.createElement('div');
        modelFactor.className = 'flex justify-between text-sm';
        modelFactor.innerHTML = `
            <span>Modèle IA:</span>
            <span class="font-medium">${factors.model_type}</span>
        `;
        factorsElement.appendChild(modelFactor);
    }

    // Analyse textuelle
    if (factors.text_analysis) {
        const textFactor = document.createElement('div');
        textFactor.className = 'flex justify-between text-sm';
        textFactor.innerHTML = `
            <span>Analyse textuelle:</span>
            <span class="font-medium">${factors.text_analysis}</span>
        `;
        factorsElement.appendChild(textFactor);
    }

    // Mots critiques détectés
    if (factors.mots_critiques_detectes !== undefined) {
        const criticalFactor = document.createElement('div');
        criticalFactor.className = 'flex justify-between text-sm';
        criticalFactor.innerHTML = `
            <span>Mots critiques détectés:</span>
            <span class="font-medium">${factors.mots_critiques_detectes}</span>
        `;
        factorsElement.appendChild(criticalFactor);
    }

    // Patterns ultra-critiques
    if (factors.patterns_ultra_critiques !== undefined) {
        const patternsFactor = document.createElement('div');
        patternsFactor.className = 'flex justify-between text-sm';
        patternsFactor.innerHTML = `
            <span>Patterns ultra-critiques:</span>
            <span class="font-medium">${factors.patterns_ultra_critiques}</span>
        `;
        factorsElement.appendChild(patternsFactor);
    }

    // Détection critique (pour cas forcés)
    if (factors.detection_critique) {
        const detectionFactor = document.createElement('div');
        detectionFactor.className = 'flex justify-between text-sm';
        detectionFactor.innerHTML = `
            <span>Détection critique:</span>
            <span class="font-medium text-red-600">${factors.detection_critique}</span>
        `;
        factorsElement.appendChild(detectionFactor);
    }

    // Raison de l'override
    if (factors.override_reason) {
        const overrideFactor = document.createElement('div');
        overrideFactor.className = 'flex justify-between text-sm';
        overrideFactor.innerHTML = `
            <span>Raison:</span>
            <span class="font-medium text-orange-600">${factors.override_reason}</span>
        `;
        factorsElement.appendChild(overrideFactor);
    }

    // Features numériques (si disponibles)
    if (factors.numeric_features) {
        const numericFactor = document.createElement('div');
        numericFactor.className = 'flex justify-between text-sm';
        numericFactor.innerHTML = `
            <span>Features numériques:</span>
            <span class="font-medium">${factors.numeric_features}</span>
        `;
        factorsElement.appendChild(numericFactor);
    }

    // Afficher les recommandations
    const recommendations = getRecommendations(prediction.niveau_predit, factors);
    document.getElementById('recommendations').innerHTML = recommendations;
}

function getLevelLabel(niveau) {
    if (niveau.includes('Critique')) return 'Critique';
    if (niveau.includes('Mineur')) return 'Mineur';
    return 'Tolérable';
}

function getLevelColor(niveau) {
    if (niveau.includes('Critique')) return 'bg-red-100 text-red-800 border border-red-200';
    if (niveau.includes('Mineur')) return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
    return 'bg-green-100 text-green-800 border border-green-200';
}

function getRecommendations(niveau, factors) {
    let recommendations = [];

    if (niveau.includes('Critique')) {
        recommendations.push('Action immédiate requise');
        recommendations.push('Mettre en place un plan de mitigation');
        recommendations.push('Informer la direction');
    } else if (niveau.includes('Mineur')) {
        recommendations.push('Surveillance recommandée');
        recommendations.push('Documenter les mesures préventives');
        recommendations.push('Révision périodique');
    } else {
        recommendations.push('Risque acceptable');
        recommendations.push('Suivi dans le cadre normal');
        recommendations.push('Réévaluation si contexte change');
    }

    // Recommandations basées sur les facteurs
    if (factors.critical_keywords > 3) {
        recommendations.push('Vocabulaire critique détecté - vérifier la gravité réelle');
    }

    if (!factors.has_causes) {
        recommendations.push('Ajouter des causes détaillées pour améliorer l\'analyse');
    }

    if (!factors.has_effets) {
        recommendations.push('Préciser les effets pour une meilleure évaluation');
    }

    return recommendations.map(rec => `<div>${rec}</div>`).join('');
}

async function loadExample(index) {
    try {
        // S'assurer que les exemples sont chargés
        if (predictionExamples.length === 0) {
            await loadPredictionExamples();
        }

        if (index >= 0 && index < predictionExamples.length) {
            const example = predictionExamples[index];

            // Remplir les champs de l'IA Avancée
            const processusSelect = document.getElementById('advanced-processus');
            const processusValue = example.processus || '';

            // Vérifier si la valeur existe dans les options
            if (processusSelect) {
                const optionExists = Array.from(processusSelect.options).some(option => option.value === processusValue);
                if (optionExists) {
                    processusSelect.value = processusValue;
                } else {
                    console.warn(`Processus "${processusValue}" non trouvé dans les options. Utilisation de la valeur par défaut.`);
                    processusSelect.value = ''; // Valeur par défaut
                }
            }

            // Remplir les autres champs
            const descField = document.getElementById('advanced-description');
            const causesField = document.getElementById('advanced-causes');
            const effetsField = document.getElementById('advanced-effets');

            if (descField) descField.value = example.description || '';
            if (causesField) causesField.value = example.causes || '';
            if (effetsField) effetsField.value = example.effets || '';

            // Faire automatiquement la prédiction avec l'IA Avancée
            setTimeout(() => {
                if (typeof predictAdvanced === 'function') {
                    predictAdvanced();
                } else {
                    console.log('Fonction predictAdvanced non disponible');
                }
            }, 500);
        } else {
            console.error('Index d\'exemple invalide:', index, 'Exemples disponibles:', predictionExamples.length);
            showError('Exemple non trouvé. Veuillez réessayer.');
        }
    } catch (error) {
        console.error('Erreur lors du chargement de l\'exemple:', error);
        showError('Erreur lors du chargement de l\'exemple');
    }
}

// Fonction pour prédire le risque dans le formulaire de création
async function predictCurrentRisk() {
    const description = document.getElementById('description').value;
    const causes = document.getElementById('causes').value;
    const effets = document.getElementById('effets').value;
    const processus = document.getElementById('processus').value;

    if (!description.trim()) {
        showError('Veuillez saisir une description du risque avant de faire une prédiction');
        return;
    }

    // Afficher le loading
    document.getElementById('predict-btn-text').classList.add('hidden');
    document.getElementById('predict-loading').classList.remove('hidden');

    try {
        const predictionData = {
            description: description,
            causes: causes || null,
            effets: effets || null,
            processus: processus || null
        };

        const response = await axios.post('/api/predict/risque', predictionData);
        const prediction = response.data;

        // Afficher le résultat de prédiction
        displayRiskPredictionResult(prediction);

        showSuccess('Prédiction IA réalisée avec succès!');

    } catch (error) {
        console.error('Erreur lors de la prédiction:', error);
        showError('Erreur lors de la prédiction IA');
    } finally {
        // Masquer le loading
        document.getElementById('predict-btn-text').classList.remove('hidden');
        document.getElementById('predict-loading').classList.add('hidden');
    }
}

function displayRiskPredictionResult(prediction) {
    // Afficher la zone de résultat
    document.getElementById('risk-prediction-result').classList.remove('hidden');

    // Afficher le niveau prédit
    const levelElement = document.getElementById('predicted-risk-level');
    const levelLabel = getLevelLabel(prediction.niveau_predit);
    levelElement.textContent = levelLabel;
    levelElement.className = `px-2 py-1 rounded text-sm font-medium ${getLevelColorClasses(prediction.niveau_predit)}`;

    // Afficher la confiance
    const confidence = Math.round(prediction.confiance * 100);
    document.getElementById('predicted-confidence').textContent = `${confidence}%`;
}

function getLevelColorClasses(niveau) {
    if (niveau.includes('Critique')) return 'bg-red-100 text-red-800';
    if (niveau.includes('Mineur')) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
}

// ===== FONCTIONS LLAMA =====

let llamaExamples = [];

async function checkLlamaStatus() {
    try {
        const response = await axios.get('/api/llama/status');
        const status = response.data;

        const statusElement = document.getElementById('llama-status');

        if (status.llama_available) {
            statusElement.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-green-700">✅ Llama disponible</span>
                </div>
                <div class="text-sm text-gray-600 mt-2">
                    Modèle: ${status.model_info.model} | Type: ${status.model_info.type}
                </div>
            `;
        } else {
            statusElement.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span class="text-red-700">❌ Llama non disponible</span>
                </div>
                <div class="text-sm text-gray-600 mt-2">
                    Veuillez installer Ollama (voir instructions ci-dessous)
                </div>
            `;
        }

        // Charger les exemples Llama
        await loadLlamaExamples();

    } catch (error) {
        console.error('Erreur statut Llama:', error);
        document.getElementById('llama-status').innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <span class="text-red-700">❌ Erreur de connexion</span>
            </div>
        `;
    }
}

async function loadLlamaExamples() {
    try {
        const response = await axios.get('/api/llama/examples');
        llamaExamples = response.data.examples;
    } catch (error) {
        console.error('Erreur chargement exemples Llama:', error);
    }
}

async function predictWithLlama() {
    const description = document.getElementById('llama-description').value;
    const causes = document.getElementById('llama-causes').value;
    const effets = document.getElementById('llama-effets').value;
    const processus = document.getElementById('llama-processus').value;

    if (!description.trim()) {
        showError('Veuillez saisir une description du risque');
        return;
    }

    // Afficher le loading
    document.getElementById('llama-btn-text').classList.add('hidden');
    document.getElementById('llama-loading').classList.remove('hidden');

    try {
        const predictionData = {
            description: description,
            causes: causes || null,
            effets: effets || null,
            processus: processus || null
        };

        const response = await axios.post('/api/llama/predict', predictionData);
        const prediction = response.data;

        displayLlamaResults(prediction);
        showSuccess('Analyse Llama réalisée avec succès!');

    } catch (error) {
        console.error('Erreur prédiction Llama:', error);
        if (error.response && error.response.status === 503) {
            showError('Llama non disponible. Veuillez installer Ollama.');
        } else {
            showError('Erreur lors de l\'analyse Llama');
        }
    } finally {
        // Masquer le loading
        document.getElementById('llama-btn-text').classList.remove('hidden');
        document.getElementById('llama-loading').classList.add('hidden');
    }
}

function displayLlamaResults(prediction) {
    // Masquer le placeholder et afficher les résultats
    document.getElementById('llama-placeholder').classList.add('hidden');
    document.getElementById('llama-results').classList.remove('hidden');

    // Afficher le niveau prédit
    const levelElement = document.getElementById('llama-predicted-level');
    levelElement.textContent = getLevelLabel(prediction.niveau_predit);
    levelElement.className = `text-2xl font-bold p-3 rounded text-center ${getLevelColor(prediction.niveau_predit)}`;

    // Afficher la confiance
    const confidence = Math.round(prediction.confiance * 100);
    document.getElementById('llama-confidence-bar').style.width = `${confidence}%`;
    document.getElementById('llama-confidence-text').textContent = `${confidence}% de confiance`;

    // Afficher la justification
    const justification = prediction.facteurs_influents.justification || 'Analyse Llama';
    document.getElementById('llama-justification').textContent = justification;

    // Afficher les facteurs clés
    const factorsElement = document.getElementById('llama-factors');
    const facteurs = prediction.facteurs_influents.facteurs_cles || [];
    factorsElement.innerHTML = facteurs.map(facteur =>
        `<span class="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded mr-2 mb-2">${facteur}</span>`
    ).join('');

    // Afficher les recommandations
    const recommendationsElement = document.getElementById('llama-recommendations');
    const recommendations = prediction.facteurs_influents.recommandations || [];
    recommendationsElement.innerHTML = recommendations.map(rec =>
        `<div class="flex items-start space-x-2 mb-2">
            <span class="text-purple-600">•</span>
            <span class="text-sm">${rec}</span>
        </div>`
    ).join('');
}

async function loadLlamaExample(index) {
    try {
        // S'assurer que les exemples sont chargés
        if (llamaExamples.length === 0) {
            await loadLlamaExamples();
        }

        if (index >= 0 && index < llamaExamples.length) {
            const example = llamaExamples[index];

            document.getElementById('llama-processus').value = example.processus || '';
            document.getElementById('llama-description').value = example.description || '';
            document.getElementById('llama-causes').value = example.causes || '';
            document.getElementById('llama-effets').value = example.effets || '';

            // Faire automatiquement la prédiction
            setTimeout(() => {
                predictWithLlama();
            }, 500);
        } else {
            console.error('Index d\'exemple Llama invalide:', index);
            showError('Exemple Llama non trouvé');
        }
    } catch (error) {
        console.error('Erreur chargement exemple Llama:', error);
        showError('Erreur lors du chargement de l\'exemple Llama');
    }
}

async function hybridPredict() {
    const description = document.getElementById('llama-description').value;
    const causes = document.getElementById('llama-causes').value;
    const effets = document.getElementById('llama-effets').value;
    const processus = document.getElementById('llama-processus').value;

    if (!description.trim()) {
        showError('Veuillez saisir une description du risque');
        return;
    }

    try {
        const predictionData = {
            description: description,
            causes: causes || null,
            effets: effets || null,
            processus: processus || null
        };

        const response = await axios.post('/api/llama/hybrid-predict', predictionData);
        const results = response.data.hybrid_results;

        // Afficher les résultats hybrides
        displayHybridResults(results);
        showSuccess('Analyse hybride réalisée avec succès!');

    } catch (error) {
        console.error('Erreur prédiction hybride:', error);
        showError('Erreur lors de l\'analyse hybride');
    }
}

function displayHybridResults(results) {
    // Créer une modal ou section pour afficher les résultats hybrides
    const modalHtml = `
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onclick="closeHybridModal()">
            <div class="bg-white p-6 rounded-lg max-w-4xl max-h-96 overflow-y-auto" onclick="event.stopPropagation()">
                <h3 class="text-xl font-bold mb-4">🔄 Analyse Hybride: Random Forest + Llama</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    ${results.random_forest && !results.random_forest.error ? `
                    <div class="border rounded p-4">
                        <h4 class="font-semibold text-blue-600 mb-2">🌳 Random Forest</h4>
                        <div class="space-y-2">
                            <div><strong>Niveau:</strong> ${results.random_forest.niveau_predit}</div>
                            <div><strong>Confiance:</strong> ${Math.round(results.random_forest.confiance * 100)}%</div>
                            <div class="text-sm text-gray-600">Analyse rapide basée sur les patterns appris</div>
                        </div>
                    </div>
                    ` : '<div class="border rounded p-4 bg-gray-50"><h4 class="text-gray-500">Random Forest non disponible</h4></div>'}

                    ${results.llama && !results.llama.error ? `
                    <div class="border rounded p-4">
                        <h4 class="font-semibold text-purple-600 mb-2">🦙 Llama</h4>
                        <div class="space-y-2">
                            <div><strong>Niveau:</strong> ${results.llama.niveau_predit}</div>
                            <div><strong>Confiance:</strong> ${Math.round(results.llama.confiance * 100)}%</div>
                            <div class="text-sm text-gray-600">Analyse contextuelle approfondie</div>
                        </div>
                    </div>
                    ` : '<div class="border rounded p-4 bg-gray-50"><h4 class="text-gray-500">Llama non disponible</h4></div>'}
                </div>

                ${results.synthesis ? `
                <div class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded">
                    <h4 class="font-semibold mb-2">🎯 Synthèse</h4>
                    <div><strong>Consensus:</strong> ${results.synthesis.consensus}</div>
                    <div><strong>Recommandation:</strong> ${results.synthesis.niveau_recommande}</div>
                    <div><strong>Confiance finale:</strong> ${Math.round(results.synthesis.confiance_finale * 100)}%</div>
                </div>
                ` : ''}

                <button onclick="closeHybridModal()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Fermer
                </button>
            </div>
        </div>
    `;

    // Ajouter la modal au DOM
    const modalDiv = document.createElement('div');
    modalDiv.id = 'hybrid-modal';
    modalDiv.innerHTML = modalHtml;
    document.body.appendChild(modalDiv);
}

function closeHybridModal() {
    const modal = document.getElementById('hybrid-modal');
    if (modal) {
        modal.remove();
    }
}

// Fonctions utilitaires pour Llama
function getLevelLabel(niveau) {
    if (niveau.includes('Critique')) return 'Critique (IPR > 18)';
    if (niveau.includes('Mineur')) return 'Mineur (8 ≤ IPR ≤ 18)';
    if (niveau.includes('Tolérable')) return 'Tolérable (IPR < 8)';
    return niveau;
}

function getLevelColor(niveau) {
    if (niveau.includes('Critique')) return 'bg-red-100 text-red-800 border border-red-200';
    if (niveau.includes('Mineur')) return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
    return 'bg-green-100 text-green-800 border border-green-200';
}

// Fonction pour nettoyer les formulaires
function clearLlamaForm() {
    document.getElementById('llama-processus').value = '';
    document.getElementById('llama-description').value = '';
    document.getElementById('llama-causes').value = '';
    document.getElementById('llama-effets').value = '';

    // Masquer les résultats
    document.getElementById('llama-results').classList.add('hidden');
    document.getElementById('llama-placeholder').classList.remove('hidden');
}

// Fonction pour copier les résultats Llama
function copyLlamaResults() {
    const niveau = document.getElementById('llama-predicted-level').textContent;
    const confiance = document.getElementById('llama-confidence-text').textContent;
    const justification = document.getElementById('llama-justification').textContent;

    const text = `Analyse Llama:
Niveau: ${niveau}
Confiance: ${confiance}
Justification: ${justification}`;

    navigator.clipboard.writeText(text).then(() => {
        showSuccess('Résultats copiés dans le presse-papiers');
    }).catch(() => {
        showError('Erreur lors de la copie');
    });
}

// ===== FONCTIONS MÉTHODE DE COTATION =====

async function loadMethodeCotation() {
    try {
        // Charger les échelles de cotation
        const response = await axios.get('/api/methode-cotation/echelles');
        const echelles = response.data;

        // Charger les règles de criticité
        const reglesResponse = await axios.get('/api/methode-cotation/regles-criticite');
        const regles = reglesResponse.data;

        // Charger les exemples
        const exemplesResponse = await axios.get('/api/methode-cotation/exemples-calcul');
        const exemples = exemplesResponse.data;

        console.log('Méthode de cotation chargée:', { echelles, regles, exemples });

    } catch (error) {
        console.error('Erreur chargement méthode de cotation:', error);
        showError('Erreur lors du chargement de la méthode de cotation');
    }
}

async function calculerIPRDemo(detection, gravite, frequence) {
    try {
        const response = await axios.post('/api/methode-cotation/calculer-ipr', {
            detection,
            gravite,
            frequence
        });

        return response.data;
    } catch (error) {
        console.error('Erreur calcul IPR:', error);
        return null;
    }
}






