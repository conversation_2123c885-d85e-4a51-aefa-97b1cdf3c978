from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from ..models.database import get_db
from ..schemas import PredictionRequest, PredictionResponse

router = APIRouter(prefix="/api/predict", tags=["prediction"])

@router.post("/risque", response_model=PredictionResponse)
def predict_risk_level(
    prediction_request: PredictionRequest,
    db: Session = Depends(get_db)
):
    """Prédit le niveau de criticité d'un nouveau risque (version simplifiée)"""
    
    try:
        # Logique de prédiction simplifiée basée sur des règles
        description = prediction_request.description.lower()
        causes = (prediction_request.causes or "").lower()
        effets = (prediction_request.effets or "").lower()
        
        # Mots-clés critiques
        critical_keywords = [
            'critique', 'urgent', 'grave', 'important', 'majeur', 'sévère',
            'perte', 'arrêt', 'échec', 'défaillance', 'problème',
            'impact', 'conséquence', 'dommage', 'coût', 'retard'
        ]
        
        # Compter les mots-clés critiques
        text_combined = f"{description} {causes} {effets}"
        critical_count = sum(1 for keyword in critical_keywords if keyword in text_combined)
        
        # Calculer la longueur du texte
        text_length = len(text_combined)
        
        # Logique de prédiction simple
        if critical_count >= 4 or 'arrêt' in text_combined or 'critique' in text_combined:
            niveau_predit = "Critique (IPR > 18)"
            confiance = 0.8
        elif critical_count >= 2 or text_length > 100:
            niveau_predit = "Mineur (8 ≤ IPR ≤ 18)"
            confiance = 0.7
        else:
            niveau_predit = "Tolérable (IPR < 8)"
            confiance = 0.6
        
        # Facteurs influents
        facteurs_influents = {
            'text_length': text_length,
            'critical_keywords': critical_count,
            'has_causes': bool(prediction_request.causes and prediction_request.causes.strip()),
            'has_effets': bool(prediction_request.effets and prediction_request.effets.strip()),
            'processus': prediction_request.processus or 'Unknown'
        }
        
        return PredictionResponse(
            niveau_predit=niveau_predit,
            confiance=confiance,
            facteurs_influents=facteurs_influents
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la prédiction: {str(e)}")

@router.get("/factors")
def get_prediction_factors():
    """Récupère les facteurs qui influencent les prédictions"""
    
    return {
        "facteurs_textuels": [
            "Longueur de la description",
            "Présence de mots-clés critiques",
            "Présence de causes détaillées",
            "Présence d'effets détaillés"
        ],
        "mots_cles_critiques": [
            "critique", "urgent", "grave", "important", "majeur", "sévère",
            "perte", "arrêt", "échec", "défaillance", "problème",
            "impact", "conséquence", "dommage", "coût", "retard"
        ],
        "conseils": [
            "Plus la description est détaillée, plus la prédiction est précise",
            "Inclure les causes et effets améliore la qualité de la prédiction",
            "Utiliser un vocabulaire précis et technique"
        ]
    }

@router.get("/model/info")
def get_model_info():
    """Récupère les informations sur le modèle de prédiction"""
    
    return {
        "model_info": {
            "status": "active",
            "model_type": "Rule-based (simplified)",
            "classes": [
                "Tolérable (IPR < 8)",
                "Mineur (8 ≤ IPR ≤ 18)",
                "Critique (IPR > 18)"
            ]
        },
        "performance": {
            "accuracy": "Estimation basée sur des règles",
            "classes_supported": [
                "Tolérable (IPR < 8)",
                "Mineur (8 ≤ IPR ≤ 18)",
                "Critique (IPR > 18)"
            ],
            "features_count": "Analyse textuelle + règles métier"
        },
        "usage_stats": {
            "model_version": "1.0-simplified",
            "note": "Version simplifiée basée sur des règles métier"
        }
    }

@router.get("/examples")
def get_prediction_examples():
    """Récupère des exemples de prédictions pour tester le modèle"""
    
    return {
        "examples": [
            {
                "name": "Risque Critique - Panne Serveur",
                "description": "Panne critique du serveur principal causant un arrêt complet de la production",
                "causes": "Défaillance matérielle majeure, surcharge du système, absence de redondance",
                "effets": "Arrêt total de la production, perte de revenus importante, retards de livraison clients",
                "processus": "Production",
                "expected_level": "Critique"
            },
            {
                "name": "Risque Mineur - Erreur Facturation",
                "description": "Erreur occasionnelle dans la facturation client",
                "causes": "Erreur de saisie manuelle, formation insuffisante",
                "effets": "Mécontentement client ponctuel, correction manuelle nécessaire",
                "processus": "Facturation et recouvrement",
                "expected_level": "Mineur"
            },
            {
                "name": "Risque Tolérable - Formation",
                "description": "Formation du personnel pas toujours à jour",
                "causes": "Budget formation limité, planning chargé",
                "effets": "Légère baisse de productivité, adaptation plus lente aux nouveautés",
                "processus": "Gestion des ressources humaines",
                "expected_level": "Tolérable"
            }
        ]
    }
