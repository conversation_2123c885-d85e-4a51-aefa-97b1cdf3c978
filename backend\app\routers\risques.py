from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ..models.database import get_db
from ..schemas import (
    RisqueCreate, RisqueUpdate, RisqueResponse,
    PaginatedResponse
)
from ..services.risque_service import RisqueService

router = APIRouter(prefix="/api/risques", tags=["risques"])

@router.post("/", response_model=RisqueResponse)
def create_risque(
    risque_data: RisqueCreate,
    db: Session = Depends(get_db)
):
    """Crée un nouveau risque"""
    try:
        risque = RisqueService.create_risque(db, risque_data)
        return risque
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/", response_model=List[RisqueResponse])
def get_risques(
    skip: int = Query(0, ge=0),
    limit: int = Query(1000, ge=1, le=10000),  # Augmenté de 100 à 1000
    processus: Optional[str] = Query(None),
    niveau_criticite: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Récupère la liste des risques avec filtres optionnels"""
    risques = RisqueService.get_risques(
        db=db,
        skip=skip,
        limit=limit,
        processus=processus,
        niveau_criticite=niveau_criticite,
        search=search
    )
    return risques

@router.get("/{risque_id}", response_model=RisqueResponse)
def get_risque(
    risque_id: int,
    db: Session = Depends(get_db)
):
    """Récupère un risque par son ID"""
    risque = RisqueService.get_risque(db, risque_id)
    if not risque:
        raise HTTPException(status_code=404, detail="Risque non trouvé")
    return risque

@router.put("/{risque_id}", response_model=RisqueResponse)
def update_risque(
    risque_id: int,
    risque_data: RisqueUpdate,
    db: Session = Depends(get_db)
):
    """Met à jour un risque existant"""
    risque = RisqueService.update_risque(db, risque_id, risque_data)
    if not risque:
        raise HTTPException(status_code=404, detail="Risque non trouvé")
    return risque

@router.delete("/{risque_id}")
def delete_risque(
    risque_id: int,
    db: Session = Depends(get_db)
):
    """Supprime un risque"""
    success = RisqueService.delete_risque(db, risque_id)
    if not success:
        raise HTTPException(status_code=404, detail="Risque non trouvé")
    return {"message": "Risque supprimé avec succès"}

@router.get("/processus/list")
def get_processus_list(db: Session = Depends(get_db)):
    """Récupère la liste des processus uniques"""
    from sqlalchemy import distinct
    from ..models.risque import Risque
    
    processus = db.query(distinct(Risque.processus)).all()
    return [p[0] for p in processus if p[0]]

@router.get("/stats/distribution")
def get_distribution_stats(db: Session = Depends(get_db)):
    """Récupère les statistiques de distribution des risques"""
    from sqlalchemy import func
    from ..models.risque import Risque
    
    # Distribution par niveau de criticité
    criticite_stats = db.query(
        Risque.niveau_criticite,
        func.count(Risque.id)
    ).group_by(Risque.niveau_criticite).all()
    
    # Distribution par processus
    processus_stats = db.query(
        Risque.processus,
        func.count(Risque.id)
    ).group_by(Risque.processus).all()
    
    return {
        "criticite": [{"niveau": stat[0], "count": stat[1]} for stat in criticite_stats],
        "processus": [{"processus": stat[0], "count": stat[1]} for stat in processus_stats]
    }
