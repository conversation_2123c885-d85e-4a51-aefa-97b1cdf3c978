from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ..models.database import get_db
from ..schemas import (
    OpportuniteCreate, OpportuniteUpdate, OpportuniteResponse
)
from ..services.risque_service import OpportuniteService

router = APIRouter(prefix="/api/opportunites", tags=["opportunites"])

@router.post("/", response_model=OpportuniteResponse)
def create_opportunite(
    opportunite_data: OpportuniteCreate,
    db: Session = Depends(get_db)
):
    """Crée une nouvelle opportunité"""
    try:
        opportunite = OpportuniteService.create_opportunite(db, opportunite_data)
        return opportunite
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/", response_model=List[OpportuniteResponse])
def get_opportunites(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    processus: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Récupère la liste des opportunités avec filtres optionnels"""
    opportunites = OpportuniteService.get_opportunites(
        db=db,
        skip=skip,
        limit=limit,
        processus=processus
    )
    return opportunites

@router.get("/{opportunite_id}", response_model=OpportuniteResponse)
def get_opportunite(
    opportunite_id: int,
    db: Session = Depends(get_db)
):
    """Récupère une opportunité par son ID"""
    opportunite = OpportuniteService.get_opportunite(db, opportunite_id)
    if not opportunite:
        raise HTTPException(status_code=404, detail="Opportunité non trouvée")
    return opportunite

@router.put("/{opportunite_id}", response_model=OpportuniteResponse)
def update_opportunite(
    opportunite_id: int,
    opportunite_data: OpportuniteUpdate,
    db: Session = Depends(get_db)
):
    """Met à jour une opportunité existante"""
    opportunite = OpportuniteService.update_opportunite(db, opportunite_id, opportunite_data)
    if not opportunite:
        raise HTTPException(status_code=404, detail="Opportunité non trouvée")
    return opportunite

@router.delete("/{opportunite_id}")
def delete_opportunite(
    opportunite_id: int,
    db: Session = Depends(get_db)
):
    """Supprime une opportunité"""
    success = OpportuniteService.delete_opportunite(db, opportunite_id)
    if not success:
        raise HTTPException(status_code=404, detail="Opportunité non trouvée")
    return {"message": "Opportunité supprimée avec succès"}
