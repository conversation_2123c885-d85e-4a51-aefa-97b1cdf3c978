import pandas as pd
import os

# Tester différents chemins
paths_to_try = [
    '../Analyse des risques et opportunités.xls',
    os.path.join('..', 'Analyse des risques et opportunités.xls'),
    'C:/Users/<USER>/Documents/augment-projects/Risques/Analyse des risques et opportunités.xls'
]

for path in paths_to_try:
    print(f"Tentative avec: {path}")
    print(f"Chemin absolu: {os.path.abspath(path)}")
    print(f"Existe: {os.path.exists(path)}")
    
    if os.path.exists(path):
        try:
            xl = pd.ExcelFile(path)
            print(f"Feuilles: {xl.sheet_names}")
            break
        except Exception as e:
            print(f"Erreur: {e}")
    print("---")
