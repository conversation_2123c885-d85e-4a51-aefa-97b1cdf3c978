"""
Script pour importer les données Excel existantes dans la base de données
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
from sqlalchemy.orm import sessionmaker
from app.models.database import engine, Base
from app.models.risque import Risque, Opportunite
from datetime import datetime
import re

# Créer les tables
Base.metadata.create_all(bind=engine)

# Créer une session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def clean_text(text):
    """Nettoie le texte en supprimant les caractères indésirables"""
    if pd.isna(text) or text is None:
        return None
    
    text = str(text).strip()
    if text.lower() in ['nan', 'none', '']:
        return None
    
    return text

def parse_date(date_str):
    """Parse une date depuis différents formats"""
    if pd.isna(date_str) or date_str is None:
        return None
    
    if isinstance(date_str, datetime):
        return date_str
    
    date_str = str(date_str).strip()
    if date_str.lower() in ['nan', 'none', '', 'en continue']:
        return None
    
    # Essayer différents formats de date
    date_formats = [
        "%Y-%m-%d",
        "%d/%m/%Y",
        "%m/%d/%Y",
        "%Y-%m-%d %H:%M:%S",
        "%d-%m-%Y"
    ]
    
    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    return None

def import_risques():
    """Importe les données de risques depuis le CSV nettoyé"""
    
    print("Importation des risques...")
    
    # Lire le fichier CSV nettoyé
    df = pd.read_csv('donnees_risques_nettoyees.csv')
    
    db = SessionLocal()
    
    try:
        imported_count = 0
        
        for index, row in df.iterrows():
            try:
                # Nettoyer et valider les données
                processus = clean_text(row.get('processus'))
                description = clean_text(row.get('risque'))
                
                if not processus or not description:
                    print(f"Ligne {index}: Processus ou description manquant, ignorée")
                    continue
                
                # Créer l'objet Risque
                risque = Risque(
                    processus=processus,
                    source=clean_text(row.get('source')),
                    enjeux=clean_text(row.get('enjeux')),
                    description=description,
                    causes=clean_text(row.get('causes')),
                    effets=clean_text(row.get('effets')),
                    moyens_detection=clean_text(row.get('moyens_detection')),
                    note_detection=int(row.get('note_detection', 1)),
                    note_gravite=int(row.get('note_gravite', 1)),
                    note_frequence=int(row.get('note_frequence', 1)),
                    moyens_maitrise=clean_text(row.get('moyens_maitrise')),
                    actions=clean_text(row.get('actions')),
                    responsables=clean_text(row.get('responsables')),
                    delai=parse_date(row.get('delai')),
                    methode_evaluation=clean_text(row.get('methode_evaluation')),
                    date_mesure=parse_date(row.get('date_mesure')),
                    evaluation_efficacite=clean_text(row.get('evaluation_efficacite'))
                )
                
                # Calculer les champs automatiques
                risque.update_calculated_fields()
                
                # Ajouter à la base de données
                db.add(risque)
                imported_count += 1
                
                if imported_count % 10 == 0:
                    print(f"Importé {imported_count} risques...")
                    
            except Exception as e:
                print(f"Erreur ligne {index}: {e}")
                continue
        
        # Sauvegarder toutes les modifications
        db.commit()
        print(f"✅ Importation terminée: {imported_count} risques importés")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'importation: {e}")
        db.rollback()
    finally:
        db.close()

def import_opportunites():
    """Importe les données d'opportunités si disponibles"""
    
    print("\nImportation des opportunités...")
    
    try:
        # Lire le fichier CSV des opportunités
        df = pd.read_csv('donnees_opportunites.csv')
        
        # Analyser la structure pour identifier les colonnes utiles
        print(f"Colonnes disponibles: {list(df.columns)}")
        print(f"Nombre de lignes: {len(df)}")
        
        # Pour l'instant, on skip l'import des opportunités car la structure n'est pas claire
        print("⚠️  Structure des opportunités à analyser manuellement")
        
    except FileNotFoundError:
        print("⚠️  Fichier des opportunités non trouvé")
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse des opportunités: {e}")

def verify_import():
    """Vérifie que l'importation s'est bien passée"""
    
    print("\nVérification de l'importation...")
    
    db = SessionLocal()
    
    try:
        # Compter les risques
        total_risques = db.query(Risque).count()
        print(f"Total des risques en base: {total_risques}")
        
        # Distribution par processus
        from sqlalchemy import func
        processus_stats = db.query(
            Risque.processus,
            func.count(Risque.id)
        ).group_by(Risque.processus).all()
        
        print("\nDistribution par processus:")
        for processus, count in processus_stats:
            print(f"  {processus}: {count}")
        
        # Distribution par criticité
        criticite_stats = db.query(
            Risque.niveau_criticite,
            func.count(Risque.id)
        ).group_by(Risque.niveau_criticite).all()
        
        print("\nDistribution par criticité:")
        for criticite, count in criticite_stats:
            print(f"  {criticite}: {count}")
        
        # Quelques exemples
        print("\nExemples de risques importés:")
        exemples = db.query(Risque).limit(3).all()
        for risque in exemples:
            print(f"  ID {risque.id}: {risque.processus} - {risque.description[:50]}...")
        
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Début de l'importation des données")
    print("="*50)
    
    # Importer les risques
    import_risques()
    
    # Importer les opportunités
    import_opportunites()
    
    # Vérifier l'importation
    verify_import()
    
    print("\n✅ Importation terminée!")
    print("Vous pouvez maintenant démarrer l'API avec: uvicorn app.main:app --reload")
